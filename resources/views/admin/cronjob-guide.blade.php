<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hướng dẫn thiết lập Cronjob - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>📅 Hướng dẫn thiết lập Cronjob</h1>
                    <a href="{{ route('admin.dashboard') }}" class="btn btn-secondary">← Quay lại Dashboard</a>
                </div>

                <!-- Server Info -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5>🖥️ Thông tin Server</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>PHP Version:</strong><br>
                                <span class="badge bg-success">{{ $serverInfo['php_version'] }}</span>
                            </div>
                            <div class="col-md-3">
                                <strong>Laravel Version:</strong><br>
                                <span class="badge bg-primary">{{ $serverInfo['laravel_version'] }}</span>
                            </div>
                            <div class="col-md-3">
                                <strong>Operating System:</strong><br>
                                <span class="badge bg-secondary">{{ $serverInfo['os'] }}</span>
                            </div>
                            <div class="col-md-3">
                                <strong>Project Path:</strong><br>
                                <code class="small">{{ $serverInfo['project_path'] }}</code>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Setup -->
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5>⚡ Thiết lập nhanh</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <strong>📋 Copy và paste lệnh này vào crontab:</strong>
                        </div>
                        <div class="bg-dark text-light p-3 rounded position-relative">
                            <code id="cronjob-command">* * * * * cd {{ $serverInfo['project_path'] }} && php artisan schedule:run >> /dev/null 2>&1</code>
                            <button class="btn btn-sm btn-outline-light position-absolute top-0 end-0 m-2" onclick="copyToClipboard()">📋 Copy</button>
                        </div>
                    </div>
                </div>

                <!-- Detailed Instructions -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header bg-primary text-white">
                                <h5>🐧 Linux/Ubuntu Server</h5>
                            </div>
                            <div class="card-body">
                                <h6>Bước 1: Mở crontab editor</h6>
                                <div class="bg-dark text-light p-2 rounded mb-3">
                                    <code>crontab -e</code>
                                </div>

                                <h6>Bước 2: Thêm dòng cronjob</h6>
                                <div class="bg-dark text-light p-2 rounded mb-3">
                                    <code>* * * * * cd {{ $serverInfo['project_path'] }} && php artisan schedule:run >> /dev/null 2>&1</code>
                                </div>

                                <h6>Bước 3: Lưu và thoát</h6>
                                <ul>
                                    <li>Nano: <kbd>Ctrl + X</kbd>, sau đó <kbd>Y</kbd>, <kbd>Enter</kbd></li>
                                    <li>Vim: <kbd>:wq</kbd></li>
                                </ul>

                                <div class="alert alert-info">
                                    <strong>💡 Mẹo:</strong> Sử dụng <code>crontab -l</code> để xem danh sách cronjob hiện tại
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-header bg-warning text-dark">
                                <h5>🏠 Shared Hosting (cPanel)</h5>
                            </div>
                            <div class="card-body">
                                <h6>Bước 1: Đăng nhập cPanel</h6>
                                <p>Tìm mục "Cron Jobs" hoặc "Tác vụ định thời"</p>

                                <h6>Bước 2: Tạo Cron Job mới</h6>
                                <table class="table table-sm">
                                    <tr><td><strong>Minute:</strong></td><td><code>*</code></td></tr>
                                    <tr><td><strong>Hour:</strong></td><td><code>*</code></td></tr>
                                    <tr><td><strong>Day:</strong></td><td><code>*</code></td></tr>
                                    <tr><td><strong>Month:</strong></td><td><code>*</code></td></tr>
                                    <tr><td><strong>Weekday:</strong></td><td><code>*</code></td></tr>
                                </table>

                                <h6>Command:</h6>
                                <div class="bg-light p-2 rounded border">
                                    <code>/usr/local/bin/php {{ $serverInfo['project_path'] }}/artisan schedule:run</code>
                                </div>

                                <div class="alert alert-warning mt-3">
                                    <strong>⚠️ Lưu ý:</strong> Đường dẫn PHP có thể khác nhau tùy hosting provider
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Schedule Details -->
                <div class="card mt-4">
                    <div class="card-header bg-secondary text-white">
                        <h5>⏰ Lịch trình gửi tin nhắn</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center p-3 border rounded">
                                    <h6 class="text-success">🌅 Buổi sáng</h6>
                                    <h4>8:00 AM</h4>
                                    <small>Nhắc nhở users có lịch "morning"</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3 border rounded">
                                    <h6 class="text-warning">☀️ Buổi trưa</h6>
                                    <h4>12:00 PM</h4>
                                    <small>Nhắc nhở users có lịch "afternoon"</small>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center p-3 border rounded">
                                    <h6 class="text-primary">🌙 Buổi tối</h6>
                                    <h4>8:00 PM</h4>
                                    <small>Nhắc nhở users có lịch "evening"</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Testing -->
                <div class="card mt-4">
                    <div class="card-header bg-info text-white">
                        <h5>🧪 Test và Troubleshooting</h5>
                    </div>
                    <div class="card-body">
                        <h6>Test Commands:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="bg-light p-3 rounded">
                                    <strong>Xem scheduled tasks:</strong><br>
                                    <code>php artisan schedule:list</code>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="bg-light p-3 rounded">
                                    <strong>Test gửi thủ công:</strong><br>
                                    <code>php artisan vocabulary:send-questions --time=morning</code>
                                </div>
                            </div>
                        </div>

                        <h6 class="mt-3">Kiểm tra Log:</h6>
                        <div class="bg-dark text-light p-2 rounded">
                            <code>tail -f {{ $serverInfo['project_path'] }}/storage/logs/laravel.log</code>
                        </div>

                        <div class="alert alert-success mt-3">
                            <h6>✅ Dấu hiệu hoạt động đúng:</h6>
                            <ul class="mb-0">
                                <li>Users nhận được tin nhắn nhắc nhở đúng giờ</li>
                                <li>Không có lỗi trong log file</li>
                                <li>Database được cập nhật với thời gian gửi</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Common Issues -->
                <div class="card mt-4 mb-4">
                    <div class="card-header bg-danger text-white">
                        <h5>🚨 Các lỗi thường gặp</h5>
                    </div>
                    <div class="card-body">
                        <div class="accordion" id="troubleshootAccordion">
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#issue1">
                                        Cronjob không chạy
                                    </button>
                                </h2>
                                <div id="issue1" class="accordion-collapse collapse" data-bs-parent="#troubleshootAccordion">
                                    <div class="accordion-body">
                                        <ul>
                                            <li>Kiểm tra đường dẫn PHP: <code>which php</code></li>
                                            <li>Kiểm tra quyền thực thi: <code>chmod +x artisan</code></li>
                                            <li>Kiểm tra crontab: <code>crontab -l</code></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="accordion-item">
                                <h2 class="accordion-header">
                                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#issue2">
                                        Permission denied
                                    </button>
                                </h2>
                                <div id="issue2" class="accordion-collapse collapse" data-bs-parent="#troubleshootAccordion">
                                    <div class="accordion-body">
                                        <ul>
                                            <li>Cấp quyền cho thư mục: <code>chmod -R 755 storage bootstrap/cache</code></li>
                                            <li>Đổi owner: <code>chown -R www-data:www-data .</code></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function copyToClipboard() {
            const command = document.getElementById('cronjob-command').textContent;
            navigator.clipboard.writeText(command).then(function() {
                alert('✅ Đã copy lệnh cronjob vào clipboard!');
            });
        }
    </script>
</body>
</html>
