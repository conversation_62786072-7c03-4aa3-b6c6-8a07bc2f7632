<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chi tiết học viên - {{ $user->first_name }} {{ $user->last_name }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>👤 {{ $user->first_name }} {{ $user->last_name }}</h1>
                    <div>
                        <a href="{{ route('admin.detailed-stats') }}" class="btn btn-secondary">← Quay lại danh sách</a>
                        <a href="{{ route('admin.dashboard') }}" class="btn btn-outline-secondary">🏠 Dashboard</a>
                    </div>
                </div>

                <!-- User Info -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header bg-primary text-white">
                                <h5>📋 Thông tin cơ bản</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Tên:</strong></td>
                                        <td>{{ $user->first_name }} {{ $user->last_name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Username:</strong></td>
                                        <td>@{{ $user->username ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Telegram ID:</strong></td>
                                        <td><code>{{ $user->telegram_id }}</code></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Ngôn ngữ:</strong></td>
                                        <td>{{ $user->language_code ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Trạng thái:</strong></td>
                                        <td>
                                            @if($user->is_active)
                                                <span class="badge bg-success">✅ Hoạt động</span>
                                            @else
                                                <span class="badge bg-secondary">⏸️ Tạm dừng</span>
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Session học:</strong></td>
                                        <td>
                                            @if($user->is_in_learning_session)
                                                <span class="badge bg-warning">📚 Đang học ({{ $user->current_session_count }})</span>
                                            @else
                                                <span class="badge bg-light text-dark">Không</span>
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Lịch học:</strong></td>
                                        <td>
                                            @if($user->schedule_times)
                                                @foreach($user->schedule_times as $time)
                                                    <span class="badge bg-info me-1">{{ $time }}</span>
                                                @endforeach
                                            @else
                                                <span class="text-muted">Chưa thiết lập</span>
                                            @endif
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Hoạt động cuối:</strong></td>
                                        <td>{{ $user->last_activity ? $user->last_activity->diffForHumans() : 'Chưa có' }}</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-8">
                        <!-- Statistics Cards -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <div class="card text-center border-primary">
                                    <div class="card-body">
                                        <i class="fas fa-book fa-2x text-primary mb-2"></i>
                                        <h4 class="text-primary">{{ $userStats['total_words'] }}</h4>
                                        <small>Tổng từ vựng</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center border-success">
                                    <div class="card-body">
                                        <i class="fas fa-check fa-2x text-success mb-2"></i>
                                        <h4 class="text-success">{{ $userStats['correct_answers'] }}</h4>
                                        <small>Trả lời đúng</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center border-danger">
                                    <div class="card-body">
                                        <i class="fas fa-times fa-2x text-danger mb-2"></i>
                                        <h4 class="text-danger">{{ $userStats['wrong_answers'] }}</h4>
                                        <small>Trả lời sai</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center border-info">
                                    <div class="card-body">
                                        <i class="fas fa-bullseye fa-2x text-info mb-2"></i>
                                        <h4 class="text-info">{{ $userStats['accuracy'] }}%</h4>
                                        <small>Độ chính xác</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3">
                                <div class="card text-center border-warning">
                                    <div class="card-body">
                                        <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                                        <h4 class="text-warning">{{ $userStats['due_words'] }}</h4>
                                        <small>Cần ôn tập</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center border-success">
                                    <div class="card-body">
                                        <i class="fas fa-trophy fa-2x text-success mb-2"></i>
                                        <h4 class="text-success">{{ $userStats['mastered_words'] }}</h4>
                                        <small>Thành thạo</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center border-primary">
                                    <div class="card-body">
                                        <i class="fas fa-graduation-cap fa-2x text-primary mb-2"></i>
                                        <h4 class="text-primary">{{ $userStats['learning_words'] }}</h4>
                                        <small>Đang học</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center border-secondary">
                                    <div class="card-body">
                                        <i class="fas fa-plus fa-2x text-secondary mb-2"></i>
                                        <h4 class="text-secondary">{{ $userStats['new_words'] }}</h4>
                                        <small>Từ mới</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Progress Chart -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>📈 Tiến độ 7 ngày gần nhất</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="progressChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>📊 Phân bố theo độ khó</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="difficultyChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>🕒 Hoạt động gần đây</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Từ vựng</th>
                                        <th>Định nghĩa</th>
                                        <th>Kết quả</th>
                                        <th>Lần thành công</th>
                                        <th>Lần tới</th>
                                        <th>Thời gian</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentActivity as $activity)
                                    <tr>
                                        <td><strong>{{ $activity->vocabulary->word }}</strong></td>
                                        <td>{{ Str::limit($activity->vocabulary->definition, 50) }}</td>
                                        <td>
                                            @if($activity->last_result)
                                                <span class="badge bg-success">✅ Đúng</span>
                                            @else
                                                <span class="badge bg-danger">❌ Sai</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-info">{{ $activity->success_count }}/5</span>
                                        </td>
                                        <td>
                                            @if($activity->next_show <= now())
                                                <span class="badge bg-warning">Ngay bây giờ</span>
                                            @else
                                                <small>{{ $activity->next_show->diffForHumans() }}</small>
                                            @endif
                                        </td>
                                        <td>
                                            <small>{{ $activity->updated_at->diffForHumans() }}</small>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Words by Difficulty -->
                <div class="card">
                    <div class="card-header">
                        <h5>📚 Từ vựng theo độ khó</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach($wordsByLevel as $level => $words)
                            <div class="col-md-4 mb-3">
                                <div class="card border-{{ $level == 'beginner' ? 'success' : ($level == 'intermediate' ? 'warning' : 'danger') }}">
                                    <div class="card-header bg-{{ $level == 'beginner' ? 'success' : ($level == 'intermediate' ? 'warning' : 'danger') }} text-white">
                                        <h6 class="mb-0">
                                            {{ $level == 'beginner' ? '🟢 Cơ bản' : ($level == 'intermediate' ? '🟡 Trung bình' : '🔴 Nâng cao') }}
                                            ({{ $words->count() }} từ)
                                        </h6>
                                    </div>
                                    <div class="card-body" style="max-height: 200px; overflow-y: auto;">
                                        @foreach($words->take(10) as $word)
                                        <div class="d-flex justify-content-between align-items-center mb-1">
                                            <small><strong>{{ $word->vocabulary->word }}</strong></small>
                                            <span class="badge bg-{{ $word->success_count >= 5 ? 'success' : ($word->success_count > 0 ? 'warning' : 'secondary') }}">
                                                {{ $word->success_count }}/5
                                            </span>
                                        </div>
                                        @endforeach
                                        @if($words->count() > 10)
                                        <small class="text-muted">... và {{ $words->count() - 10 }} từ khác</small>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Progress Chart
        const progressCtx = document.getElementById('progressChart').getContext('2d');
        const progressData = @json($progressData);
        
        const progressChart = new Chart(progressCtx, {
            type: 'line',
            data: {
                labels: Object.keys(progressData),
                datasets: [{
                    label: 'Trả lời đúng',
                    data: Object.values(progressData).map(d => d.correct),
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                }, {
                    label: 'Trả lời sai',
                    data: Object.values(progressData).map(d => d.wrong),
                    borderColor: 'rgb(255, 99, 132)',
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });

        // Difficulty Chart
        const difficultyCtx = document.getElementById('difficultyChart').getContext('2d');
        const difficultyData = @json($wordsByLevel);
        
        const difficultyChart = new Chart(difficultyCtx, {
            type: 'doughnut',
            data: {
                labels: Object.keys(difficultyData).map(level => 
                    level === 'beginner' ? 'Cơ bản' : 
                    level === 'intermediate' ? 'Trung bình' : 'Nâng cao'
                ),
                datasets: [{
                    data: Object.values(difficultyData).map(words => words.length),
                    backgroundColor: [
                        'rgba(75, 192, 192, 0.8)',
                        'rgba(255, 206, 86, 0.8)',
                        'rgba(255, 99, 132, 0.8)'
                    ],
                    borderColor: [
                        'rgba(75, 192, 192, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(255, 99, 132, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    </script>
</body>
</html>
