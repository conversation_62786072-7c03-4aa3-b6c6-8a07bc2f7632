<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Import từ vựng - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header">
                        <h4>📁 Import từ vựng từ file</h4>
                        <small class="text-muted">Hiện có {{ $totalVocabs }} từ vựng trong database</small>
                    </div>
                    <div class="card-body">
                        @if(session('success'))
                            <div class="alert alert-success">{{ session('success') }}</div>
                        @endif
                        
                        @if(session('error'))
                            <div class="alert alert-danger">{{ session('error') }}</div>
                        @endif

                        @if($errors->any())
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    @foreach($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                        <form action="{{ route('admin.import-vocabulary') }}" method="POST" enctype="multipart/form-data">
                            @csrf
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="vocabulary_file" class="form-label">Chọn file từ vựng *</label>
                                        <input type="file" class="form-control" id="vocabulary_file" name="vocabulary_file" 
                                               accept=".txt,.csv" required>
                                        <div class="form-text">
                                            Hỗ trợ file .txt hoặc .csv. Mỗi dòng một từ vựng.
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="batch_size" class="form-label">Batch size</label>
                                        <select class="form-control" id="batch_size" name="batch_size">
                                            <option value="5">5 từ/batch</option>
                                            <option value="10" selected>10 từ/batch</option>
                                            <option value="20">20 từ/batch</option>
                                            <option value="50">50 từ/batch</option>
                                        </select>
                                        <div class="form-text">
                                            Số từ xử lý cùng lúc (tránh rate limit API)
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    🚀 Bắt đầu Import
                                </button>
                            </div>
                        </form>

                        <hr class="my-4">

                        <!-- Instructions -->
                        <div class="alert alert-info">
                            <h6>💡 Hướng dẫn sử dụng:</h6>
                            <ol class="mb-2">
                                <li><strong>Chuẩn bị file:</strong> Tạo file .txt với mỗi dòng là một từ vựng tiếng Anh</li>
                                <li><strong>Upload file:</strong> Chọn file và batch size phù hợp</li>
                                <li><strong>Xử lý tự động:</strong> Hệ thống sẽ:
                                    <ul>
                                        <li>Kiểm tra từ trùng lặp (bỏ qua nếu đã có)</li>
                                        <li>Gọi OpenAI để tạo định nghĩa tiếng Việt</li>
                                        <li>Tạo 3 đáp án sai hợp lý</li>
                                        <li>Sinh mẹo ghi nhớ</li>
                                        <li>Lưu vào database</li>
                                    </ul>
                                </li>
                            </ol>
                            
                            <h6>📝 Format file mẫu:</h6>
                            <pre class="bg-light p-2 rounded">Hello
Hi
Good morning
abandon
beautiful
challenge</pre>
                        </div>

                        <!-- Sample file download -->
                        <div class="alert alert-secondary">
                            <h6>📥 File mẫu có sẵn:</h6>
                            <p class="mb-2">Bạn có thể sử dụng file <code>vocabulary.txt</code> đã có trong thư mục gốc của dự án.</p>
                            <p class="mb-0">
                                <strong>Lệnh command line:</strong><br>
                                <code>php artisan vocabulary:import vocabulary.txt --batch=10 --delay=2</code>
                            </p>
                        </div>

                        <!-- Progress info -->
                        <div class="alert alert-warning">
                            <h6>⚠️ Lưu ý quan trọng:</h6>
                            <ul class="mb-0">
                                <li>Quá trình import có thể mất vài phút tùy thuộc số lượng từ</li>
                                <li>Mỗi từ cần gọi OpenAI API nên có delay giữa các batch</li>
                                <li>Từ đã tồn tại sẽ được bỏ qua tự động</li>
                                <li>Kiểm tra log nếu có lỗi: <code>storage/logs/laravel.log</code></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Quick stats -->
                <div class="row mt-4">
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">📚 Tổng từ vựng</h5>
                                <h2 class="text-primary">{{ $totalVocabs }}</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">🤖 AI Generated</h5>
                                <h2 class="text-success">{{ $totalVocabs }}</h2>
                                <small class="text-muted">Câu hỏi tự động</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <h5 class="card-title">⚡ Batch Import</h5>
                                <h2 class="text-info">✓</h2>
                                <small class="text-muted">Xử lý hàng loạt</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-4">
                    <a href="{{ route('admin.dashboard') }}" class="btn btn-secondary">
                        ← Quay lại Dashboard
                    </a>
                    <a href="{{ route('admin.add-vocabulary') }}" class="btn btn-outline-primary">
                        ➕ Thêm từ đơn lẻ
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
