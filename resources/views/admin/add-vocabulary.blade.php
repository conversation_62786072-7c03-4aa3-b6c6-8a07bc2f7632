<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thêm từ vựng - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h4>➕ Thêm từ vựng mới</h4>
                    </div>
                    <div class="card-body">
                        @if(session('success'))
                            <div class="alert alert-success">{{ session('success') }}</div>
                        @endif
                        
                        @if(session('error'))
                            <div class="alert alert-danger">{{ session('error') }}</div>
                        @endif

                        @if($errors->any())
                            <div class="alert alert-danger">
                                <ul class="mb-0">
                                    @foreach($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                        <form action="{{ route('admin.add-vocabulary') }}" method="POST">
                            @csrf
                            <div class="mb-3">
                                <label for="word" class="form-label">Từ vựng (tiếng Anh) *</label>
                                <input type="text" class="form-control" id="word" name="word" 
                                       value="{{ old('word') }}" required>
                                <div class="form-text">Ví dụ: abandon, beautiful, challenge</div>
                            </div>

                            <div class="mb-3">
                                <label for="definition" class="form-label">Định nghĩa (tiếng Việt)</label>
                                <input type="text" class="form-control" id="definition" name="definition" 
                                       value="{{ old('definition') }}">
                                <div class="form-text">
                                    Để trống nếu muốn AI tự động tạo định nghĩa và câu hỏi trắc nghiệm
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    🤖 Tạo từ vựng với AI
                                </button>
                            </div>
                        </form>

                        <hr class="my-4">

                        <div class="alert alert-info">
                            <h6>💡 Cách hoạt động:</h6>
                            <ul class="mb-0">
                                <li>Nhập từ vựng tiếng Anh</li>
                                <li>Hệ thống sẽ gọi OpenAI để tạo:
                                    <ul>
                                        <li>Định nghĩa tiếng Việt</li>
                                        <li>3 đáp án sai hợp lý</li>
                                        <li>Mẹo ghi nhớ</li>
                                    </ul>
                                </li>
                                <li>Từ vựng sẽ được lưu vào database</li>
                                <li>Người dùng sẽ nhận được câu hỏi trắc nghiệm</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="mt-3">
                    <a href="{{ route('admin.dashboard') }}" class="btn btn-secondary">
                        ← Quay lại Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
