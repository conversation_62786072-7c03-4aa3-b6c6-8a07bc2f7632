<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Học <PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">🎯 Admin Dashboard - Hệ thống học từ vựng</h1>

        @if(session('success'))
            <div class="alert alert-success">{{ session('success') }}</div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger">{{ session('error') }}</div>
        @endif

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card text-white bg-primary">
                    <div class="card-body">
                        <h5 class="card-title">📚 Tổng từ vựng</h5>
                        <h2>{{ $totalVocabs }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-white bg-success">
                    <div class="card-body">
                        <h5 class="card-title">👥 Tổng người dùng</h5>
                        <h2>{{ $totalUsers }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-white bg-info">
                    <div class="card-body">
                        <h5 class="card-title">✅ Người dùng hoạt động</h5>
                        <h2>{{ $activeUsers }}</h2>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>🔧 Thiết lập Webhook</h5>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('admin.webhook') }}" method="POST">
                            @csrf
                            <div class="mb-3">
                                <label for="webhook_url" class="form-label">Webhook URL:</label>
                                <input type="url" class="form-control" id="webhook_url" name="webhook_url"
                                       placeholder="https://yourdomain.com/api/telegram/webhook" required>
                            </div>
                            <button type="submit" class="btn btn-primary">Thiết lập Webhook</button>
                        </form>

                        <hr>

                        <form action="{{ route('admin.test-proxy') }}" method="POST">
                            @csrf
                            <button type="submit" class="btn btn-info">🌐 Test Proxy Connection</button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>🧪 Test câu hỏi</h5>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('admin.test-question') }}" method="POST">
                            @csrf
                            <div class="mb-3">
                                <label for="telegram_id" class="form-label">Telegram ID:</label>
                                <input type="number" class="form-control" id="telegram_id" name="telegram_id" required>
                            </div>
                            <div class="mb-3">
                                <label for="vocab_id" class="form-label">Vocabulary ID:</label>
                                <input type="number" class="form-control" id="vocab_id" name="vocab_id" value="1" required>
                            </div>
                            <button type="submit" class="btn btn-warning">Gửi câu hỏi test</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <div class="mt-4">
            <a href="{{ route('admin.add-vocabulary') }}" class="btn btn-success">➕ Thêm từ vựng</a>
            <a href="{{ route('admin.import-vocabulary') }}" class="btn btn-primary">📁 Import từ file</a>
            <a href="#" class="btn btn-info">📊 Xem thống kê chi tiết</a>
        </div>

        <!-- Instructions -->
        <div class="card mt-4">
            <div class="card-header">
                <h5>📋 Hướng dẫn sử dụng</h5>
            </div>
            <div class="card-body">
                <ol>
                    <li><strong>Thiết lập Webhook:</strong> Nhập URL webhook của bạn để Telegram có thể gửi tin nhắn đến hệ thống.</li>
                    <li><strong>Thêm từ vựng:</strong> Sử dụng form thêm từ vựng hoặc import từ file CSV.</li>
                    <li><strong>Test câu hỏi:</strong> Gửi câu hỏi test đến một Telegram ID cụ thể để kiểm tra.</li>
                    <li><strong>Cronjob:</strong> Thiết lập cronjob chạy lệnh <code>php artisan schedule:run</code> mỗi phút.</li>
                </ol>

                <h6>Lệnh hữu ích:</h6>
                <ul>
                    <li><code>php artisan vocabulary:send-questions --time=morning</code> - Gửi câu hỏi buổi sáng</li>
                    <li><code>php artisan db:seed --class=VocabularySeeder</code> - Thêm dữ liệu mẫu</li>
                    <li><code>php artisan telegram:test-proxy --chat-id=YOUR_ID</code> - Test kết nối proxy</li>
                    <li><code>php artisan vocabulary:import vocabulary.txt</code> - Import từ vựng từ file</li>
                </ul>

                <h6>🌐 Proxy Configuration:</h6>
                <div class="alert alert-info mb-0">
                    <strong>Telegram API Proxy:</strong> <code>telegram.devqanh.workers.dev</code><br>
                    <small>✅ Sử dụng proxy để tránh bị chặn IP tại Việt Nam</small>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
