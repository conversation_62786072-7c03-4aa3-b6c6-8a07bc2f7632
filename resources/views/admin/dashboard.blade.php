<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Học <PERSON></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">🎯 Admin Dashboard - Hệ thống học từ vựng</h1>

        @if(session('success'))
            <div class="alert alert-success">{{ session('success') }}</div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger">{{ session('error') }}</div>
        @endif

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card text-white bg-primary">
                    <div class="card-body">
                        <h5 class="card-title">📚 Tổng từ vựng</h5>
                        <h2>{{ $totalVocabs }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-white bg-success">
                    <div class="card-body">
                        <h5 class="card-title">👥 Tổng người dùng</h5>
                        <h2>{{ $totalUsers }}</h2>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card text-white bg-info">
                    <div class="card-body">
                        <h5 class="card-title">✅ Người dùng hoạt động</h5>
                        <h2>{{ $activeUsers }}</h2>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>🔧 Thiết lập Webhook</h5>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('admin.webhook') }}" method="POST">
                            @csrf
                            <div class="mb-3">
                                <label for="webhook_url" class="form-label">Webhook URL:</label>
                                <input type="url" class="form-control" id="webhook_url" name="webhook_url"
                                       placeholder="https://yourdomain.com/api/telegram/webhook" required>
                            </div>
                            <button type="submit" class="btn btn-primary">Thiết lập Webhook</button>
                        </form>

                        <hr>

                        <form action="{{ route('admin.test-proxy') }}" method="POST">
                            @csrf
                            <button type="submit" class="btn btn-info">🌐 Test Proxy Connection</button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>🧪 Test câu hỏi</h5>
                    </div>
                    <div class="card-body">
                        <form action="{{ route('admin.test-question') }}" method="POST">
                            @csrf
                            <div class="mb-3">
                                <label for="telegram_id" class="form-label">Telegram ID:</label>
                                <input type="number" class="form-control" id="telegram_id" name="telegram_id" required>
                            </div>
                            <div class="mb-3">
                                <label for="vocab_id" class="form-label">Vocabulary ID:</label>
                                <input type="number" class="form-control" id="vocab_id" name="vocab_id" value="1" required>
                            </div>
                            <button type="submit" class="btn btn-warning">Gửi câu hỏi test</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation -->
        <div class="mt-4">
            <a href="{{ route('admin.add-vocabulary') }}" class="btn btn-success">➕ Thêm từ vựng</a>
            <a href="{{ route('admin.import-vocabulary') }}" class="btn btn-primary">📁 Import từ file</a>
            <a href="{{ route('admin.cronjob-guide') }}" class="btn btn-warning">📅 Hướng dẫn Cronjob</a>
            <a href="#" class="btn btn-info">📊 Xem thống kê chi tiết</a>
        </div>

        <!-- Instructions -->
        <div class="card mt-4">
            <div class="card-header">
                <h5>📋 Hướng dẫn sử dụng</h5>
            </div>
            <div class="card-body">
                <ol>
                    <li><strong>Thiết lập Webhook:</strong> Nhập URL webhook của bạn để Telegram có thể gửi tin nhắn đến hệ thống.</li>
                    <li><strong>Thêm từ vựng:</strong> Sử dụng form thêm từ vựng hoặc import từ file CSV.</li>
                    <li><strong>Test câu hỏi:</strong> Gửi câu hỏi test đến một Telegram ID cụ thể để kiểm tra.</li>
                    <li><strong>Thiết lập Cronjob:</strong>
                        <a href="{{ route('admin.cronjob-guide') }}" class="btn btn-sm btn-primary">📅 Hướng dẫn chi tiết</a>
                        <a href="#cronjob-setup" class="btn btn-sm btn-outline-secondary">📋 Xem nhanh</a>
                    </li>
                </ol>

                <h6>Lệnh hữu ích:</h6>
                <ul>
                    <li><code>php artisan vocabulary:send-questions --time=morning</code> - Gửi câu hỏi buổi sáng</li>
                    <li><code>php artisan db:seed --class=VocabularySeeder</code> - Thêm dữ liệu mẫu</li>
                    <li><code>php artisan telegram:test-proxy --chat-id=YOUR_ID</code> - Test kết nối proxy</li>
                    <li><code>php artisan vocabulary:import vocabulary.txt</code> - Import từ vựng từ file</li>
                    <li><code>php artisan bot:test-commands CHAT_ID</code> - Test bot commands</li>
                </ul>

                <h6>🤖 Bot Commands mới:</h6>
                <div class="alert alert-success mb-0">
                    <strong>/learn</strong> - Học 1 từ ngay lập tức (chủ động)<br>
                    <strong>/learn_more</strong> - Học 5 từ liên tiếp (session học)<br>
                    <small>✨ Người dùng không cần chờ đến giờ định sẵn nữa!</small>
                </div>

                <h6>🌐 Proxy Configuration:</h6>
                <div class="alert alert-info mb-0">
                    <strong>Telegram API Proxy:</strong> <code>telegram.devqanh.workers.dev</code><br>
                    <small>✅ Sử dụng proxy để tránh bị chặn IP tại Việt Nam</small>
                </div>
            </div>
        </div>

        <!-- Cronjob Setup Guide -->
        <div class="card mt-4" id="cronjob-setup">
            <div class="card-header bg-warning">
                <h5>📅 Hướng dẫn thiết lập Cronjob chi tiết</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <strong>⚠️ Quan trọng:</strong> Cronjob cần thiết để hệ thống tự động gửi nhắc nhở học tập theo lịch trình.
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h6>🖥️ Linux/Ubuntu Server:</h6>
                        <div class="bg-dark text-light p-3 rounded">
                            <code>
# Mở crontab editor<br>
crontab -e<br><br>
# Thêm dòng này vào cuối file:<br>
* * * * * cd /path/to/your/project && php artisan schedule:run >> /dev/null 2>&1
                            </code>
                        </div>

                        <div class="mt-3">
                            <strong>Thay thế đường dẫn:</strong><br>
                            <code>/path/to/your/project</code> → Đường dẫn thực tế đến thư mục dự án
                        </div>
                    </div>

                    <div class="col-md-6">
                        <h6>🏠 Shared Hosting (cPanel):</h6>
                        <ol>
                            <li>Đăng nhập cPanel</li>
                            <li>Tìm "Cron Jobs" hoặc "Tác vụ định thời"</li>
                            <li>Tạo cron job mới với cấu hình:</li>
                        </ol>

                        <div class="bg-light p-3 rounded border">
                            <strong>Minute:</strong> *<br>
                            <strong>Hour:</strong> *<br>
                            <strong>Day:</strong> *<br>
                            <strong>Month:</strong> *<br>
                            <strong>Weekday:</strong> *<br>
                            <strong>Command:</strong><br>
                            <code>/usr/local/bin/php /home/<USER>/public_html/artisan schedule:run</code>
                        </div>
                    </div>
                </div>

                <hr>

                <div class="row mt-4">
                    <div class="col-md-4">
                        <div class="card border-success">
                            <div class="card-header bg-success text-white">
                                <h6 class="mb-0">🌅 Buổi sáng (8:00)</h6>
                            </div>
                            <div class="card-body">
                                <small>Gửi nhắc nhở học từ vựng buổi sáng cho users đã đăng ký lịch morning</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-warning">
                            <div class="card-header bg-warning text-dark">
                                <h6 class="mb-0">☀️ Buổi trưa (12:00)</h6>
                            </div>
                            <div class="card-body">
                                <small>Gửi nhắc nhở học từ vựng buổi trưa cho users đã đăng ký lịch afternoon</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card border-primary">
                            <div class="card-header bg-primary text-white">
                                <h6 class="mb-0">🌙 Buổi tối (20:00)</h6>
                            </div>
                            <div class="card-body">
                                <small>Gửi nhắc nhở học từ vựng buổi tối cho users đã đăng ký lịch evening</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="alert alert-warning mt-4">
                    <h6>🔧 Test Cronjob:</h6>
                    <p>Sau khi thiết lập, test bằng các lệnh sau:</p>
                    <ul class="mb-0">
                        <li><code>php artisan schedule:list</code> - Xem danh sách scheduled tasks</li>
                        <li><code>php artisan vocabulary:send-questions --time=morning</code> - Test gửi thủ công</li>
                        <li>Kiểm tra log: <code>storage/logs/laravel.log</code></li>
                    </ul>
                </div>

                <div class="alert alert-success">
                    <h6>✅ Xác nhận hoạt động:</h6>
                    <p>Cronjob hoạt động đúng khi:</p>
                    <ul class="mb-0">
                        <li>Users nhận được tin nhắn nhắc nhở đúng giờ</li>
                        <li>Không có lỗi trong log file</li>
                        <li>Database được cập nhật với thời gian gửi tin nhắn</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Smooth scroll to cronjob section -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const cronjobLink = document.querySelector('a[href="#cronjob-setup"]');
            if (cronjobLink) {
                cronjobLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    document.getElementById('cronjob-setup').scrollIntoView({
                        behavior: 'smooth'
                    });
                });
            }
        });
    </script>
</body>
</html>
