<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thống kê chi tiết - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>📊 Thống kê chi tiết học viên</h1>
                    <a href="{{ route('admin.dashboard') }}" class="btn btn-secondary">← Quay lại Dashboard</a>
                </div>

                <!-- Overall Statistics -->
                <div class="row mb-4">
                    <div class="col-md-2">
                        <div class="card text-center border-primary">
                            <div class="card-body">
                                <i class="fas fa-users fa-2x text-primary mb-2"></i>
                                <h4 class="text-primary">{{ $totalStats['total_users'] }}</h4>
                                <small>Tổng học viên</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center border-success">
                            <div class="card-body">
                                <i class="fas fa-user-check fa-2x text-success mb-2"></i>
                                <h4 class="text-success">{{ $totalStats['active_users'] }}</h4>
                                <small>Đang hoạt động</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center border-warning">
                            <div class="card-body">
                                <i class="fas fa-play fa-2x text-warning mb-2"></i>
                                <h4 class="text-warning">{{ $totalStats['users_in_session'] }}</h4>
                                <small>Đang học</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center border-info">
                            <div class="card-body">
                                <i class="fas fa-book fa-2x text-info mb-2"></i>
                                <h4 class="text-info">{{ $totalStats['total_vocabularies'] }}</h4>
                                <small>Từ vựng</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center border-secondary">
                            <div class="card-body">
                                <i class="fas fa-chart-line fa-2x text-secondary mb-2"></i>
                                <h4 class="text-secondary">{{ $totalStats['total_learning_logs'] }}</h4>
                                <small>Lượt học</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="card text-center border-danger">
                            <div class="card-body">
                                <i class="fas fa-calendar-day fa-2x text-danger mb-2"></i>
                                <h4 class="text-danger">{{ $totalStats['questions_answered_today'] }}</h4>
                                <small>Hôm nay</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Search and Filter -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <input type="text" id="searchUser" class="form-control" placeholder="🔍 Tìm kiếm học viên...">
                            </div>
                            <div class="col-md-3">
                                <select id="filterStatus" class="form-control">
                                    <option value="">Tất cả trạng thái</option>
                                    <option value="active">Đang hoạt động</option>
                                    <option value="inactive">Tạm dừng</option>
                                    <option value="in_session">Đang học</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select id="sortBy" class="form-control">
                                    <option value="total_words">Sắp xếp: Số từ học</option>
                                    <option value="accuracy">Sắp xếp: Độ chính xác</option>
                                    <option value="last_activity">Sắp xếp: Hoạt động gần nhất</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button class="btn btn-primary w-100" onclick="exportStats()">
                                    📊 Export Excel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Users Table -->
                <div class="card">
                    <div class="card-header">
                        <h5>👥 Danh sách học viên chi tiết</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover" id="usersTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>👤 Học viên</th>
                                        <th>📊 Trạng thái</th>
                                        <th>📚 Từ vựng</th>
                                        <th>✅ Đúng</th>
                                        <th>❌ Sai</th>
                                        <th>🎯 Độ chính xác</th>
                                        <th>⏰ Cần ôn</th>
                                        <th>🏆 Thành thạo</th>
                                        <th>🕒 Hoạt động cuối</th>
                                        <th>🔧 Thao tác</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($userStats as $stat)
                                    <tr class="user-row" 
                                        data-name="{{ strtolower($stat['user']->first_name . ' ' . $stat['user']->last_name) }}"
                                        data-status="{{ $stat['user']->is_active ? 'active' : 'inactive' }}{{ $stat['is_in_session'] ? ' in_session' : '' }}"
                                        data-words="{{ $stat['total_words'] }}"
                                        data-accuracy="{{ $stat['accuracy'] }}">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="me-3">
                                                    @if($stat['user']->is_active)
                                                        <span class="badge bg-success">🟢</span>
                                                    @else
                                                        <span class="badge bg-secondary">⚫</span>
                                                    @endif
                                                </div>
                                                <div>
                                                    <strong>{{ $stat['user']->first_name }} {{ $stat['user']->last_name }}</strong><br>
                                                    <small class="text-muted">@{{ $stat['user']->username ?? 'N/A' }}</small><br>
                                                    <small class="text-info">ID: {{ $stat['user']->telegram_id }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            @if($stat['is_in_session'])
                                                <span class="badge bg-warning">📚 Đang học ({{ $stat['session_count'] }})</span>
                                            @elseif($stat['user']->is_active)
                                                <span class="badge bg-success">✅ Hoạt động</span>
                                            @else
                                                <span class="badge bg-secondary">⏸️ Tạm dừng</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-primary fs-6">{{ $stat['total_words'] }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-success fs-6">{{ $stat['correct_answers'] }}</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-danger fs-6">{{ $stat['wrong_answers'] }}</span>
                                        </td>
                                        <td>
                                            @if($stat['accuracy'] >= 80)
                                                <span class="badge bg-success fs-6">{{ $stat['accuracy'] }}%</span>
                                            @elseif($stat['accuracy'] >= 60)
                                                <span class="badge bg-warning fs-6">{{ $stat['accuracy'] }}%</span>
                                            @else
                                                <span class="badge bg-danger fs-6">{{ $stat['accuracy'] }}%</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if($stat['due_words'] > 0)
                                                <span class="badge bg-warning fs-6">{{ $stat['due_words'] }}</span>
                                            @else
                                                <span class="badge bg-success fs-6">0</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge bg-info fs-6">{{ $stat['mastered_words'] }}</span>
                                        </td>
                                        <td>
                                            <small>{{ $stat['last_activity'] }}</small>
                                        </td>
                                        <td>
                                            <a href="{{ route('admin.user-detail', $stat['user']->id) }}" 
                                               class="btn btn-sm btn-outline-primary">
                                                👁️ Chi tiết
                                            </a>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Search functionality
        document.getElementById('searchUser').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            filterTable();
        });

        document.getElementById('filterStatus').addEventListener('change', function() {
            filterTable();
        });

        document.getElementById('sortBy').addEventListener('change', function() {
            sortTable(this.value);
        });

        function filterTable() {
            const searchTerm = document.getElementById('searchUser').value.toLowerCase();
            const statusFilter = document.getElementById('filterStatus').value;
            const rows = document.querySelectorAll('.user-row');

            rows.forEach(row => {
                const name = row.dataset.name;
                const status = row.dataset.status;
                
                const matchesSearch = name.includes(searchTerm);
                const matchesStatus = !statusFilter || status.includes(statusFilter);
                
                row.style.display = matchesSearch && matchesStatus ? '' : 'none';
            });
        }

        function sortTable(criteria) {
            const tbody = document.querySelector('#usersTable tbody');
            const rows = Array.from(tbody.querySelectorAll('.user-row'));
            
            rows.sort((a, b) => {
                let aVal, bVal;
                
                switch(criteria) {
                    case 'total_words':
                        aVal = parseInt(a.dataset.words);
                        bVal = parseInt(b.dataset.words);
                        return bVal - aVal;
                    case 'accuracy':
                        aVal = parseFloat(a.dataset.accuracy);
                        bVal = parseFloat(b.dataset.accuracy);
                        return bVal - aVal;
                    case 'last_activity':
                        // This would need more complex logic for proper date sorting
                        return 0;
                    default:
                        return 0;
                }
            });
            
            rows.forEach(row => tbody.appendChild(row));
        }

        function exportStats() {
            // Simple CSV export
            const table = document.getElementById('usersTable');
            const rows = table.querySelectorAll('tr:not([style*="display: none"])');
            let csv = [];
            
            rows.forEach(row => {
                const cols = row.querySelectorAll('td, th');
                const rowData = Array.from(cols).map(col => 
                    col.textContent.trim().replace(/,/g, ';')
                );
                csv.push(rowData.join(','));
            });
            
            const csvContent = csv.join('\n');
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'hoc-vien-stats-' + new Date().toISOString().split('T')[0] + '.csv';
            a.click();
            window.URL.revokeObjectURL(url);
        }
    </script>
</body>
</html>
