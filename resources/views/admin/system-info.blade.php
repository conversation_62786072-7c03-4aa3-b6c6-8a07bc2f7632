<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thông tin hệ thống - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1>🖥️ Thông tin hệ thống</h1>
                    <a href="{{ route('admin.dashboard') }}" class="btn btn-secondary">← Quay lại Dashboard</a>
                </div>

                @if(session('success'))
                    <div class="alert alert-success">{{ session('success') }}</div>
                @endif
                
                @if(session('error'))
                    <div class="alert alert-danger">{{ session('error') }}</div>
                @endif

                <!-- Quick Copy Section -->
                <div class="card mb-4 border-primary">
                    <div class="card-header bg-primary text-white">
                        <h5>📋 Copy nhanh - Cronjob Command</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <strong>📌 Lệnh cronjob cho dự án này:</strong>
                        </div>
                        <div class="input-group">
                            <input type="text" class="form-control font-monospace" id="cronjob-command" 
                                   value="{{ $systemInfo['cronjob_command'] }}" readonly>
                            <button class="btn btn-success" onclick="copyToClipboard('cronjob-command')">
                                📋 Copy
                            </button>
                        </div>
                        <small class="text-muted mt-2 d-block">
                            Paste lệnh này vào crontab của server để tự động gửi nhắc nhở học tập
                        </small>
                    </div>
                </div>

                <!-- Project Info -->
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5>📁 Thông tin dự án</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Tên dự án:</strong></td>
                                        <td>
                                            <span class="badge bg-primary">{{ $systemInfo['project_name'] }}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Đường dẫn:</strong></td>
                                        <td>
                                            <div class="input-group input-group-sm">
                                                <input type="text" class="form-control font-monospace" 
                                                       id="project-path" value="{{ $systemInfo['project_path'] }}" readonly>
                                                <button class="btn btn-outline-secondary btn-sm" 
                                                        onclick="copyToClipboard('project-path')">📋</button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Document Root:</strong></td>
                                        <td>
                                            <code>{{ $systemInfo['document_root'] }}</code>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>PHP Version:</strong></td>
                                        <td><span class="badge bg-success">{{ $systemInfo['php_version'] }}</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Laravel Version:</strong></td>
                                        <td><span class="badge bg-info">{{ $systemInfo['laravel_version'] }}</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Operating System:</strong></td>
                                        <td><span class="badge bg-secondary">{{ $systemInfo['os'] }}</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Server Software:</strong></td>
                                        <td><code>{{ $systemInfo['server_software'] }}</code></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Telegram Bot Info -->
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5>🤖 Thông tin Telegram Bot</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Bot Token:</strong></td>
                                        <td>
                                            <div class="input-group input-group-sm">
                                                <input type="password" class="form-control font-monospace" 
                                                       id="bot-token" value="{{ $systemInfo['telegram_bot_token'] }}" readonly>
                                                <button class="btn btn-outline-secondary btn-sm" 
                                                        onclick="togglePassword('bot-token')">👁️</button>
                                                <button class="btn btn-outline-secondary btn-sm" 
                                                        onclick="copyToClipboard('bot-token')">📋</button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>API URL:</strong></td>
                                        <td>
                                            <div class="input-group input-group-sm">
                                                <input type="text" class="form-control font-monospace" 
                                                       id="api-url" value="{{ $systemInfo['telegram_api_url'] }}" readonly>
                                                <button class="btn btn-outline-secondary btn-sm" 
                                                        onclick="copyToClipboard('api-url')">📋</button>
                                            </div>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-4">
                                <div class="d-grid gap-2">
                                    <form action="{{ route('admin.set-telegram-menu') }}" method="POST">
                                        @csrf
                                        <button type="submit" class="btn btn-warning w-100">
                                            📱 Thiết lập Menu Bot
                                        </button>
                                    </form>
                                    <small class="text-muted">
                                        Thiết lập menu commands cho Telegram Bot
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Commands -->
                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5>⚡ Lệnh hữu ích</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>🤖 Telegram Commands:</h6>
                                <div class="list-group list-group-flush">
                                    <div class="list-group-item d-flex justify-content-between">
                                        <code>php artisan telegram:set-menu</code>
                                        <button class="btn btn-sm btn-outline-secondary" 
                                                onclick="copyText('php artisan telegram:set-menu')">📋</button>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between">
                                        <code>php artisan telegram:test-proxy</code>
                                        <button class="btn btn-sm btn-outline-secondary" 
                                                onclick="copyText('php artisan telegram:test-proxy')">📋</button>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between">
                                        <code>php artisan bot:test-commands CHAT_ID</code>
                                        <button class="btn btn-sm btn-outline-secondary" 
                                                onclick="copyText('php artisan bot:test-commands CHAT_ID')">📋</button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <h6>📚 Vocabulary Commands:</h6>
                                <div class="list-group list-group-flush">
                                    <div class="list-group-item d-flex justify-content-between">
                                        <code>php artisan vocabulary:import vocabulary.txt</code>
                                        <button class="btn btn-sm btn-outline-secondary" 
                                                onclick="copyText('php artisan vocabulary:import vocabulary.txt')">📋</button>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between">
                                        <code>php artisan vocabulary:check --latest=10</code>
                                        <button class="btn btn-sm btn-outline-secondary" 
                                                onclick="copyText('php artisan vocabulary:check --latest=10')">📋</button>
                                    </div>
                                    <div class="list-group-item d-flex justify-content-between">
                                        <code>php artisan vocabulary:send-questions --time=morning</code>
                                        <button class="btn btn-sm btn-outline-secondary" 
                                                onclick="copyText('php artisan vocabulary:send-questions --time=morning')">📋</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bot Menu Preview -->
                <div class="card mb-4">
                    <div class="card-header bg-secondary text-white">
                        <h5>📱 Preview Menu Bot</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <strong>Menu commands sẽ hiển thị trong Telegram:</strong>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="border rounded p-3 bg-light">
                                    <div class="d-flex align-items-center mb-2">
                                        <span class="badge bg-primary me-2">/start</span>
                                        <span>🚀 Bắt đầu học từ vựng</span>
                                    </div>
                                    <div class="d-flex align-items-center mb-2">
                                        <span class="badge bg-success me-2">/learn</span>
                                        <span>📚 Bắt đầu session học liên tục</span>
                                    </div>
                                    <div class="d-flex align-items-center mb-2">
                                        <span class="badge bg-info me-2">/stats</span>
                                        <span>📊 Xem tiến độ học tập</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="border rounded p-3 bg-light">
                                    <div class="d-flex align-items-center mb-2">
                                        <span class="badge bg-warning me-2">/schedule</span>
                                        <span>⏰ Thiết lập thời gian học</span>
                                    </div>
                                    <div class="d-flex align-items-center mb-2">
                                        <span class="badge bg-danger me-2">/stop</span>
                                        <span>⏹️ Dừng session hoặc tạm dừng</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            element.select();
            element.setSelectionRange(0, 99999);
            navigator.clipboard.writeText(element.value).then(function() {
                showToast('✅ Đã copy vào clipboard!');
            });
        }

        function copyText(text) {
            navigator.clipboard.writeText(text).then(function() {
                showToast('✅ Đã copy: ' + text);
            });
        }

        function togglePassword(elementId) {
            const element = document.getElementById(elementId);
            element.type = element.type === 'password' ? 'text' : 'password';
        }

        function showToast(message) {
            // Simple toast notification
            const toast = document.createElement('div');
            toast.className = 'position-fixed top-0 end-0 m-3 alert alert-success';
            toast.style.zIndex = '9999';
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }
    </script>
</body>
</html>
