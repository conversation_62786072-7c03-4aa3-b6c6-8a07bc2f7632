<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;

class OpenAIService
{
    private Client $client;
    private string $apiUrl;
    private string $apiKey;
    private string $model;

    public function __construct()
    {
        $this->client = new Client();
        $this->apiUrl = config('openai.api_url');
        $this->apiKey = config('openai.api_key');
        $this->model = config('openai.model');
    }

    public function generateVocabularyData(string $word): ?array
    {
        $prompt = $this->buildPrompt($word);
        
        try {
            $response = $this->client->post($this->apiUrl, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->apiKey,
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'messages' => [
                        [
                            'role' => 'user',
                            'content' => $prompt
                        ]
                    ],
                    'stream' => false,
                    'model' => $this->model,
                    'temperature' => config('openai.temperature', 0.5),
                    'presence_penalty' => 0,
                    'frequency_penalty' => 0,
                    'top_p' => 1
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);
            $content = $data['choices'][0]['message']['content'] ?? '';
            
            return $this->parseResponse($content);
            
        } catch (RequestException $e) {
            Log::error('OpenAI API Error: ' . $e->getMessage());
            return null;
        }
    }

    private function buildPrompt(string $word): string
    {
        return "
Tôi cần bạn tạo câu hỏi trắc nghiệm học từ vựng tiếng Anh cho từ: {$word}

1. Định nghĩa tiếng Việt:
2. 3 đáp án sai nhưng hợp lý:
3. Mẹo ghi nhớ 1 dòng, dễ nhớ (có thể dùng hình ảnh, ví dụ, âm thanh):

Output JSON:
{
  \"definition\": \"định nghĩa tiếng Việt\",
  \"wrong_choices\": [\"đáp án sai 1\", \"đáp án sai 2\", \"đáp án sai 3\"],
  \"tip\": \"mẹo ghi nhớ\"
}
";
    }

    private function parseResponse(string $content): ?array
    {
        // Try to extract JSON from the response
        $jsonStart = strpos($content, '{');
        $jsonEnd = strrpos($content, '}');
        
        if ($jsonStart !== false && $jsonEnd !== false) {
            $jsonString = substr($content, $jsonStart, $jsonEnd - $jsonStart + 1);
            $data = json_decode($jsonString, true);
            
            if (json_last_error() === JSON_ERROR_NONE) {
                return [
                    'definition' => $data['definition'] ?? '',
                    'wrong_choices' => $data['wrong_choices'] ?? [],
                    'tip' => $data['tip'] ?? ''
                ];
            }
        }
        
        Log::warning('Failed to parse OpenAI response: ' . $content);
        return null;
    }
}
