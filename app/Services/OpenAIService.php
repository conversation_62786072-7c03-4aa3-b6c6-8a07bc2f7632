<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;

class OpenAIService
{
    private Client $client;
    private string $apiUrl;
    private string $apiKey;
    private string $model;

    public function __construct()
    {
        $this->client = new Client();
        $this->apiUrl = config('openai.api_url');
        $this->apiKey = config('openai.api_key');
        $this->model = config('openai.model');
    }

    public function generateVocabularyData(string $word): ?array
    {
        $prompt = $this->buildPrompt($word);

        try {
            $response = $this->client->post($this->apiUrl, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->apiKey,
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'messages' => [
                        [
                            'role' => 'user',
                            'content' => $prompt
                        ]
                    ],
                    'stream' => false,
                    'model' => $this->model,
                    'temperature' => config('openai.temperature', 0.5),
                    'presence_penalty' => 0,
                    'frequency_penalty' => 0,
                    'top_p' => 1
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);
            $content = $data['choices'][0]['message']['content'] ?? '';

            return $this->parseResponse($content);

        } catch (RequestException $e) {
            Log::error('OpenAI API Error: ' . $e->getMessage());
            return null;
        }
    }

    private function buildPrompt(string $word): string
    {
        return "
Tôi cần bạn tạo câu hỏi trắc nghiệm học từ vựng tiếng Anh cho từ: {$word}

1. Định nghĩa tiếng Việt chính xác và đầy đủ
2. 3 đáp án sai nhưng hợp lý (cùng chủ đề hoặc gần nghĩa)
3. Mẹo ghi nhớ bằng phương pháp \"Gợi âm hài + Tưởng tượng siêu dễ\":
   - Tìm từ tiếng Việt có âm gần giống với từ tiếng Anh
   - Tạo câu chuyện/hình ảnh hài hước, dễ tưởng tượng kết nối âm và nghĩa
   - Câu mẹo phải ngắn gọn, vui nhộn, dễ nhớ (tối đa 2 dòng)

Ví dụ:
- \"cat\" (con mèo) → \"Cắt\" → \"Cắt lông mèo để mèo đẹp hơn\"
- \"book\" (sách) → \"Búc\" → \"Búc đầu vào sách để học giỏi\"
- \"happy\" (vui vẻ) → \"Hạp pi\" → \"Hạp miệng ăn pizza thì ai cũng vui\"

Output JSON:
{
  \"definition\": \"định nghĩa tiếng Việt\",
  \"wrong_choices\": [\"đáp án sai 1\", \"đáp án sai 2\", \"đáp án sai 3\"],
  \"tip\": \"mẹo ghi nhớ bằng gợi âm hài + tưởng tượng\"
}
";
    }

    private function parseResponse(string $content): ?array
    {
        // Try to extract JSON from the response
        $jsonStart = strpos($content, '{');
        $jsonEnd = strrpos($content, '}');

        if ($jsonStart !== false && $jsonEnd !== false) {
            $jsonString = substr($content, $jsonStart, $jsonEnd - $jsonStart + 1);
            $data = json_decode($jsonString, true);

            if (json_last_error() === JSON_ERROR_NONE) {
                return [
                    'definition' => $data['definition'] ?? '',
                    'wrong_choices' => $data['wrong_choices'] ?? [],
                    'tip' => $data['tip'] ?? ''
                ];
            }
        }

        Log::warning('Failed to parse OpenAI response: ' . $content);
        return null;
    }

    public function generateTipOnly(string $word, string $definition): ?string
    {
        $prompt = $this->buildTipPrompt($word, $definition);

        try {
            $response = $this->client->post($this->apiUrl, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->apiKey,
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'messages' => [
                        [
                            'role' => 'user',
                            'content' => $prompt
                        ]
                    ],
                    'stream' => false,
                    'model' => $this->model,
                    'temperature' => 0.7, // Higher temperature for more creative tips
                    'presence_penalty' => 0,
                    'frequency_penalty' => 0,
                    'top_p' => 1
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);
            $content = $data['choices'][0]['message']['content'] ?? '';

            // Extract tip from response
            $tip = trim($content);

            // Remove quotes if present
            $tip = trim($tip, '"\'');

            return $tip ?: null;

        } catch (\Exception $e) {
            Log::error('OpenAI API Error (Tip Generation): ' . $e->getMessage());
            return null;
        }
    }

    private function buildTipPrompt(string $word, string $definition): string
    {
        return "
Tạo mẹo ghi nhớ cho từ tiếng Anh: \"{$word}\" có nghĩa \"{$definition}\"

Sử dụng phương pháp \"Gợi âm hài + Tưởng tượng siêu dễ\":

1. Tìm từ/cụm từ tiếng Việt có âm gần giống với \"{$word}\"
2. Tạo câu chuyện/hình ảnh hài hước kết nối âm và nghĩa
3. Câu mẹo phải:
   - Ngắn gọn (tối đa 2 dòng)
   - Vui nhộn, dễ tưởng tượng
   - Có ký hiệu → để kết nối âm và nghĩa

Ví dụ:
- \"cat\" → \"Cắt lông mèo để mèo đẹp hơn\"
- \"book\" → \"Búc đầu vào sách để học giỏi\"
- \"happy\" → \"Hạp miệng ăn pizza thì ai cũng vui\"
- \"water\" → \"Uốt tơ (ướt tơ) → Nước làm ướt tơ tằm\"

Chỉ trả về câu mẹo, không cần giải thích:
";
    }
}
