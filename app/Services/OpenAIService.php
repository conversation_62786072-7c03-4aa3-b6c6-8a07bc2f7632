<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;

class OpenAIService
{
    private Client $client;
    private string $apiUrl;
    private string $apiKey;
    private string $model;

    public function __construct()
    {
        $this->client = new Client();
        $this->apiUrl = config('openai.api_url');
        $this->apiKey = config('openai.api_key');
        $this->model = config('openai.model');
    }

    public function generateVocabularyData(string $word): ?array
    {
        $prompt = $this->buildPrompt($word);

        try {
            $response = $this->client->post($this->apiUrl, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->apiKey,
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'messages' => [
                        [
                            'role' => 'user',
                            'content' => $prompt
                        ]
                    ],
                    'stream' => false,
                    'model' => $this->model,
                    'temperature' => config('openai.temperature', 0.5),
                    'presence_penalty' => 0,
                    'frequency_penalty' => 0,
                    'top_p' => 1
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);
            $content = $data['choices'][0]['message']['content'] ?? '';

            return $this->parseResponse($content);

        } catch (RequestException $e) {
            Log::error('OpenAI API Error: ' . $e->getMessage());
            return null;
        }
    }

    private function buildPrompt(string $word): string
    {
        return "
Tôi cần bạn tạo câu hỏi trắc nghiệm học từ vựng tiếng Anh cho từ: {$word}

1. Định nghĩa tiếng Việt chính xác và đầy đủ
2. 3 đáp án sai nhưng hợp lý (cùng chủ đề hoặc gần nghĩa)
3. Mẹo ghi nhớ bằng phương pháp \"Giải thích gốc từ đơn giản\":
   - Giải thích nguồn gốc, cách hình thành của từ một cách đơn giản
   - Phân tích thành phần của từ (tiền tố, gốc, hậu tố) nếu có
   - Kết nối với từ gốc Latin, Greek hoặc ngôn ngữ khác
   - Câu giải thích phải ngắn gọn, dễ hiểu (tối đa 2 dòng)

Ví dụ:
- \"telephone\" → \"tele\" (xa) + \"phone\" (âm thanh) → thiết bị truyền âm thanh đi xa
- \"biology\" → \"bio\" (sống) + \"logy\" (học) → môn học về sự sống
- \"transport\" → \"trans\" (qua) + \"port\" (mang) → mang đồ vật qua chỗ khác
- \"photograph\" → \"photo\" (ánh sáng) + \"graph\" (viết/vẽ) → vẽ bằng ánh sáng

Output JSON:
{
  \"definition\": \"định nghĩa tiếng Việt\",
  \"wrong_choices\": [\"đáp án sai 1\", \"đáp án sai 2\", \"đáp án sai 3\"],
  \"tip\": \"mẹo ghi nhớ bằng giải thích gốc từ đơn giản\"
}
";
    }

    private function parseResponse(string $content): ?array
    {
        // Try to extract JSON from the response
        $jsonStart = strpos($content, '{');
        $jsonEnd = strrpos($content, '}');

        if ($jsonStart !== false && $jsonEnd !== false) {
            $jsonString = substr($content, $jsonStart, $jsonEnd - $jsonStart + 1);
            $data = json_decode($jsonString, true);

            if (json_last_error() === JSON_ERROR_NONE) {
                return [
                    'definition' => $data['definition'] ?? '',
                    'wrong_choices' => $data['wrong_choices'] ?? [],
                    'tip' => $data['tip'] ?? ''
                ];
            }
        }

        Log::warning('Failed to parse OpenAI response: ' . $content);
        return null;
    }

    public function generateTipOnly(string $word, string $definition): ?string
    {
        $prompt = $this->buildTipPrompt($word, $definition);

        try {
            $response = $this->client->post($this->apiUrl, [
                'headers' => [
                    'Authorization' => 'Bearer ' . $this->apiKey,
                    'Content-Type' => 'application/json',
                ],
                'json' => [
                    'messages' => [
                        [
                            'role' => 'user',
                            'content' => $prompt
                        ]
                    ],
                    'stream' => false,
                    'model' => $this->model,
                    'temperature' => 0.7, // Higher temperature for more creative tips
                    'presence_penalty' => 0,
                    'frequency_penalty' => 0,
                    'top_p' => 1
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);
            $content = $data['choices'][0]['message']['content'] ?? '';

            // Extract tip from response
            $tip = trim($content);

            // Remove quotes if present
            $tip = trim($tip, '"\'');

            return $tip ?: null;

        } catch (\Exception $e) {
            Log::error('OpenAI API Error (Tip Generation): ' . $e->getMessage());
            return null;
        }
    }

    private function buildTipPrompt(string $word, string $definition): string
    {
        return "
Tạo mẹo ghi nhớ cho từ tiếng Anh: \"{$word}\" có nghĩa \"{$definition}\"

Sử dụng phương pháp \"Giải thích gốc từ đơn giản\":

1. Giải thích nguồn gốc, cách hình thành của từ \"{$word}\" một cách đơn giản
2. Phân tích thành phần của từ (tiền tố, gốc, hậu tố) nếu có
3. Kết nối với từ gốc Latin, Greek hoặc ngôn ngữ khác
4. Câu giải thích phải:
   - Ngắn gọn (tối đa 2 dòng)
   - Dễ hiểu, logic
   - Giúp hiểu tại sao từ này có nghĩa như vậy

Ví dụ:
- \"telephone\" → \"tele\" (xa) + \"phone\" (âm thanh) → thiết bị truyền âm thanh đi xa
- \"biology\" → \"bio\" (sống) + \"logy\" (học) → môn học về sự sống
- \"transport\" → \"trans\" (qua) + \"port\" (mang) → mang đồ vật qua chỗ khác
- \"photograph\" → \"photo\" (ánh sáng) + \"graph\" (viết) → vẽ bằng ánh sáng
- \"international\" → \"inter\" (giữa) + \"nation\" (quốc gia) → giữa các quốc gia

Chỉ trả về câu giải thích gốc từ, không cần thêm gì khác:
";
    }
}
