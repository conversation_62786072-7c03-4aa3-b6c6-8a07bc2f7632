<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;

class TelegramService
{
    private Client $client;
    private string $botToken;
    private string $apiUrl;

    public function __construct()
    {
        $this->client = new Client();
        $this->botToken = config('telegram.bot_token');
        $this->apiUrl = config('telegram.api_url') . $this->botToken;
    }

    public function sendMessage(int $chatId, string $text, ?array $replyMarkup = null): bool
    {
        try {
            $data = [
                'chat_id' => $chatId,
                'text' => $text,
                'parse_mode' => 'HTML'
            ];

            if ($replyMarkup) {
                $data['reply_markup'] = json_encode($replyMarkup);
            }

            $response = $this->client->post($this->apiUrl . '/sendMessage', [
                'form_params' => $data
            ]);

            return $response->getStatusCode() === 200;
            
        } catch (RequestException $e) {
            Log::error('Telegram API Error: ' . $e->getMessage());
            return false;
        }
    }

    public function sendVocabularyQuestion(int $chatId, string $word, array $choices, int $vocabId): bool
    {
        $text = "📚 <b>Từ vựng:</b> {$word}\n\n";
        $text .= "Từ này có nghĩa là gì?";

        $keyboard = [
            'inline_keyboard' => []
        ];

        $labels = ['A', 'B', 'C', 'D'];
        foreach ($choices as $index => $choice) {
            $keyboard['inline_keyboard'][] = [
                [
                    'text' => $labels[$index] . '. ' . $choice,
                    'callback_data' => "answer_{$vocabId}_{$index}"
                ]
            ];
        }

        return $this->sendMessage($chatId, $text, $keyboard);
    }

    public function editMessage(int $chatId, int $messageId, string $text, ?array $replyMarkup = null): bool
    {
        try {
            $data = [
                'chat_id' => $chatId,
                'message_id' => $messageId,
                'text' => $text,
                'parse_mode' => 'HTML'
            ];

            if ($replyMarkup) {
                $data['reply_markup'] = json_encode($replyMarkup);
            }

            $response = $this->client->post($this->apiUrl . '/editMessageText', [
                'form_params' => $data
            ]);

            return $response->getStatusCode() === 200;
            
        } catch (RequestException $e) {
            Log::error('Telegram Edit Message Error: ' . $e->getMessage());
            return false;
        }
    }

    public function answerCallbackQuery(string $callbackQueryId, string $text = '', bool $showAlert = false): bool
    {
        try {
            $response = $this->client->post($this->apiUrl . '/answerCallbackQuery', [
                'form_params' => [
                    'callback_query_id' => $callbackQueryId,
                    'text' => $text,
                    'show_alert' => $showAlert
                ]
            ]);

            return $response->getStatusCode() === 200;
            
        } catch (RequestException $e) {
            Log::error('Telegram Callback Answer Error: ' . $e->getMessage());
            return false;
        }
    }

    public function setWebhook(string $url): bool
    {
        try {
            $response = $this->client->post($this->apiUrl . '/setWebhook', [
                'form_params' => [
                    'url' => $url
                ]
            ]);

            return $response->getStatusCode() === 200;
            
        } catch (RequestException $e) {
            Log::error('Telegram Webhook Error: ' . $e->getMessage());
            return false;
        }
    }
}
