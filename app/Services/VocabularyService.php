<?php

namespace App\Services;

use App\Models\Vocabulary;
use App\Models\TelegramUser;
use App\Models\UserVocabLog;
use Illuminate\Support\Collection;

class VocabularyService
{
    private OpenAIService $openAIService;

    public function __construct(OpenAIService $openAIService)
    {
        $this->openAIService = $openAIService;
    }

    public function createVocabulary(string $word, ?string $definition = null): ?Vocabulary
    {
        // Check if vocabulary already exists
        $existing = Vocabulary::where('word', $word)->first();
        if ($existing) {
            return $existing;
        }

        // Generate data using OpenAI if definition not provided
        if (!$definition) {
            $aiData = $this->openAIService->generateVocabularyData($word);
            if (!$aiData) {
                return null;
            }
            
            $definition = $aiData['definition'];
            $wrongChoices = $aiData['wrong_choices'];
            $tip = $aiData['tip'];
        } else {
            // If definition provided, generate wrong choices and tip
            $aiData = $this->openAIService->generateVocabularyData($word);
            $wrongChoices = $aiData['wrong_choices'] ?? [];
            $tip = $aiData['tip'] ?? '';
        }

        // Create choices array with correct answer first
        $choices = array_merge([$definition], $wrongChoices);

        return Vocabulary::create([
            'word' => $word,
            'definition' => $definition,
            'choices' => $choices,
            'tip' => $tip,
        ]);
    }

    public function getDueVocabularies(TelegramUser $user, int $limit = 5): Collection
    {
        return UserVocabLog::where('telegram_user_id', $user->id)
            ->where('next_show', '<=', now())
            ->with('vocabulary')
            ->orderBy('next_show')
            ->limit($limit)
            ->get();
    }

    public function initializeUserVocabulary(TelegramUser $user, Vocabulary $vocabulary): UserVocabLog
    {
        return UserVocabLog::firstOrCreate([
            'telegram_user_id' => $user->id,
            'vocabulary_id' => $vocabulary->id,
        ], [
            'next_show' => now(),
            'success_count' => 0,
            'fail_count' => 0,
            'last_result' => 'pending',
        ]);
    }

    public function processAnswer(UserVocabLog $log, int $selectedIndex): bool
    {
        $vocabulary = $log->vocabulary;
        $isCorrect = $selectedIndex === 0; // First choice is always correct

        if ($isCorrect) {
            $log->markCorrect();
        } else {
            $log->markIncorrect();
        }

        $vocabulary->incrementUsage();
        
        return $isCorrect;
    }

    public function getRandomVocabularies(int $count = 10): Collection
    {
        return Vocabulary::inRandomOrder()->limit($count)->get();
    }

    public function getUserStats(TelegramUser $user): array
    {
        $logs = UserVocabLog::where('telegram_user_id', $user->id)->get();
        
        $totalWords = $logs->count();
        $masteredWords = $logs->where('success_count', '>=', 4)->count();
        $learningWords = $logs->where('success_count', '>', 0)->where('success_count', '<', 4)->count();
        $newWords = $logs->where('success_count', 0)->count();
        
        $totalCorrect = $logs->sum('success_count');
        $totalIncorrect = $logs->sum('fail_count');
        $accuracy = $totalCorrect + $totalIncorrect > 0 
            ? round(($totalCorrect / ($totalCorrect + $totalIncorrect)) * 100, 1)
            : 0;

        return [
            'total_words' => $totalWords,
            'mastered_words' => $masteredWords,
            'learning_words' => $learningWords,
            'new_words' => $newWords,
            'accuracy' => $accuracy,
            'total_correct' => $totalCorrect,
            'total_incorrect' => $totalIncorrect,
        ];
    }

    public function addVocabulariesForUser(TelegramUser $user, int $count = 10): int
    {
        $vocabularies = $this->getRandomVocabularies($count);
        $added = 0;

        foreach ($vocabularies as $vocabulary) {
            $existing = UserVocabLog::where('telegram_user_id', $user->id)
                ->where('vocabulary_id', $vocabulary->id)
                ->exists();

            if (!$existing) {
                $this->initializeUserVocabulary($user, $vocabulary);
                $added++;
            }
        }

        return $added;
    }
}
