<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\VocabularyService;
use App\Services\TelegramService;
use App\Models\Vocabulary;
use App\Models\TelegramUser;

class AdminController extends Controller
{
    private VocabularyService $vocabularyService;
    private TelegramService $telegramService;

    public function __construct(VocabularyService $vocabularyService, TelegramService $telegramService)
    {
        $this->vocabularyService = $vocabularyService;
        $this->telegramService = $telegramService;
    }

    public function dashboard()
    {
        $totalVocabs = Vocabulary::count();
        $totalUsers = TelegramUser::count();
        $activeUsers = TelegramUser::where('is_active', true)->count();

        return view('admin.dashboard', compact('totalVocabs', 'totalUsers', 'activeUsers'));
    }

    public function addVocabulary(Request $request)
    {
        if ($request->isMethod('post')) {
            $request->validate([
                'word' => 'required|string',
                'definition' => 'nullable|string',
            ]);

            // Convert word to lowercase and check for duplicates
            $word = strtolower(trim($request->word));

            if (Vocabulary::where('word', $word)->exists()) {
                return redirect()->back()->with('error', "Từ '{$word}' đã tồn tại trong database!");
            }

            $vocabulary = $this->vocabularyService->createVocabulary(
                $word,
                $request->definition
            );

            if ($vocabulary) {
                return redirect()->back()->with('success', 'Từ vựng đã được thêm thành công!');
            } else {
                return redirect()->back()->with('error', 'Không thể tạo từ vựng. Vui lòng thử lại.');
            }
        }

        return view('admin.add-vocabulary');
    }

    public function setWebhook(Request $request)
    {
        $webhookUrl = $request->input('webhook_url');

        if ($this->telegramService->setWebhook($webhookUrl)) {
            return redirect()->back()->with('success', 'Webhook đã được thiết lập thành công!');
        } else {
            return redirect()->back()->with('error', 'Không thể thiết lập webhook.');
        }
    }

    public function testQuestion(Request $request)
    {
        $request->validate([
            'telegram_id' => 'required|integer',
            'vocab_id' => 'required|exists:vocabularies,id',
        ]);

        $vocabulary = Vocabulary::find($request->vocab_id);
        $choices = $vocabulary->shuffled_choices;

        $success = $this->telegramService->sendVocabularyQuestion(
            $request->telegram_id,
            $vocabulary->word,
            $choices,
            $vocabulary->id
        );

        if ($success) {
            return redirect()->back()->with('success', 'Câu hỏi test đã được gửi!');
        } else {
            return redirect()->back()->with('error', 'Không thể gửi câu hỏi test.');
        }
    }

    public function importVocabulary(Request $request)
    {
        if ($request->isMethod('post')) {
            $request->validate([
                'vocabulary_file' => 'required|file|mimes:txt,csv|max:2048',
                'batch_size' => 'integer|min:1|max:50',
            ]);

            $file = $request->file('vocabulary_file');
            $batchSize = $request->input('batch_size', 10);

            // Save uploaded file temporarily
            $fileName = 'import_' . time() . '.' . $file->getClientOriginalExtension();
            $filePath = $file->storeAs('imports', $fileName, 'local');
            $fullPath = storage_path('app/' . $filePath);

            try {
                // Process the import
                $result = $this->processVocabularyImport($fullPath, $batchSize);

                // Clean up temporary file
                unlink($fullPath);

                $message = "✅ Import hoàn thành! ";
                $message .= "Đã import: {$result['imported']}, ";
                $message .= "Bỏ qua: {$result['skipped']}, ";
                $message .= "Lỗi: {$result['failed']}";

                return redirect()->back()->with('success', $message);

            } catch (\Exception $e) {
                // Clean up on error
                if (file_exists($fullPath)) {
                    unlink($fullPath);
                }

                return redirect()->back()->with('error', 'Lỗi import: ' . $e->getMessage());
            }
        }

        $totalVocabs = Vocabulary::count();
        return view('admin.import-vocabulary', compact('totalVocabs'));
    }

    private function processVocabularyImport(string $filePath, int $batchSize): array
    {
        $words = $this->readWordsFromFile($filePath);

        $stats = [
            'total' => count($words),
            'imported' => 0,
            'skipped' => 0,
            'failed' => 0
        ];

        $batches = array_chunk($words, $batchSize);

        foreach ($batches as $batch) {
            foreach ($batch as $word) {
                $result = $this->processWord($word);
                $stats[$result]++;
            }

            // Small delay between batches
            usleep(500000); // 0.5 seconds
        }

        return $stats;
    }

    private function readWordsFromFile(string $filePath): array
    {
        $content = file_get_contents($filePath);
        $lines = explode("\n", $content);

        $words = [];
        foreach ($lines as $line) {
            $word = trim($line);

            // Skip empty lines and Vietnamese section headers
            if (empty($word) || $word === 'Từ vựng') {
                continue;
            }

            // Clean up the word
            $word = preg_replace('/[^\w\s\-\'\.]/u', '', $word);
            $word = trim($word);

            // Convert to lowercase
            $word = strtolower($word);

            if (!empty($word) && strlen($word) > 1) {
                $words[] = $word;
            }
        }

        return array_unique($words);
    }

    private function processWord(string $word): string
    {
        try {
            // Convert word to lowercase for consistency
            $word = strtolower(trim($word));

            // Check if word already exists
            if (Vocabulary::where('word', $word)->exists()) {
                return 'skipped';
            }

            // Generate vocabulary data using OpenAI
            $aiData = $this->vocabularyService->createVocabulary($word);

            if (!$aiData) {
                return 'failed';
            }

            return 'imported';

        } catch (\Exception $e) {
            \Log::error("Error processing word '{$word}': " . $e->getMessage());
            return 'failed';
        }
    }

    public function testProxy()
    {
        try {
            $client = new \GuzzleHttp\Client();
            $botToken = config('telegram.bot_token');
            $apiUrl = config('telegram.api_url') . $botToken;

            $response = $client->get($apiUrl . '/getMe');
            $data = json_decode($response->getBody()->getContents(), true);

            if ($data['ok']) {
                $bot = $data['result'];
                $message = "✅ Proxy kết nối thành công!\n";
                $message .= "🤖 Bot: {$bot['first_name']}\n";
                $message .= "👤 Username: @{$bot['username']}\n";
                $message .= "🌐 Proxy: telegram.devqanh.workers.dev";

                return redirect()->back()->with('success', $message);
            } else {
                return redirect()->back()->with('error', 'Bot API error: ' . ($data['description'] ?? 'Unknown error'));
            }

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Proxy connection failed: ' . $e->getMessage());
        }
    }

    public function cronjobGuide()
    {
        $projectPath = base_path();
        $serverInfo = [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'project_path' => $projectPath,
            'os' => PHP_OS,
        ];

        return view('admin.cronjob-guide', compact('serverInfo'));
    }

    public function systemInfo()
    {
        $systemInfo = [
            'project_path' => base_path(),
            'project_name' => basename(base_path()),
            'php_path' => PHP_BINARY,
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'os' => PHP_OS,
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
            'cronjob_command' => "* * * * * cd " . base_path() . " && php artisan schedule:run >> /dev/null 2>&1",
            'telegram_bot_token' => config('telegram.bot_token'),
            'telegram_api_url' => config('telegram.api_url'),
        ];

        return view('admin.system-info', compact('systemInfo'));
    }

    public function setTelegramMenu()
    {
        try {
            $client = new \GuzzleHttp\Client();
            $botToken = config('telegram.bot_token');
            $apiUrl = config('telegram.api_url') . $botToken;

            $commands = [
                ['command' => 'start', 'description' => '🚀 Bắt đầu học từ vựng'],
                ['command' => 'learn', 'description' => '📚 Bắt đầu session học liên tục'],
                ['command' => 'stats', 'description' => '📊 Xem tiến độ học tập'],
                ['command' => 'schedule', 'description' => '⏰ Thiết lập thời gian học'],
                ['command' => 'stop', 'description' => '⏹️ Dừng session hoặc tạm dừng']
            ];

            $response = $client->post($apiUrl . '/setMyCommands', [
                'json' => ['commands' => $commands]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            if ($data['ok']) {
                return redirect()->back()->with('success', '✅ Đã thiết lập menu commands cho Telegram Bot thành công!');
            } else {
                return redirect()->back()->with('error', 'Lỗi API: ' . ($data['description'] ?? 'Unknown error'));
            }

        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Lỗi kết nối: ' . $e->getMessage());
        }
    }
}
