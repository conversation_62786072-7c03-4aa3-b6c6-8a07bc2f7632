<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\VocabularyService;
use App\Services\TelegramService;
use App\Models\Vocabulary;
use App\Models\TelegramUser;

class AdminController extends Controller
{
    private VocabularyService $vocabularyService;
    private TelegramService $telegramService;

    public function __construct(VocabularyService $vocabularyService, TelegramService $telegramService)
    {
        $this->vocabularyService = $vocabularyService;
        $this->telegramService = $telegramService;
    }

    public function dashboard()
    {
        $totalVocabs = Vocabulary::count();
        $totalUsers = TelegramUser::count();
        $activeUsers = TelegramUser::where('is_active', true)->count();

        return view('admin.dashboard', compact('totalVocabs', 'totalUsers', 'activeUsers'));
    }

    public function addVocabulary(Request $request)
    {
        if ($request->isMethod('post')) {
            $request->validate([
                'word' => 'required|string|unique:vocabularies,word',
                'definition' => 'nullable|string',
            ]);

            $vocabulary = $this->vocabularyService->createVocabulary(
                $request->word,
                $request->definition
            );

            if ($vocabulary) {
                return redirect()->back()->with('success', 'Từ vựng đã được thêm thành công!');
            } else {
                return redirect()->back()->with('error', 'Không thể tạo từ vựng. Vui lòng thử lại.');
            }
        }

        return view('admin.add-vocabulary');
    }

    public function setWebhook(Request $request)
    {
        $webhookUrl = $request->input('webhook_url');

        if ($this->telegramService->setWebhook($webhookUrl)) {
            return redirect()->back()->with('success', 'Webhook đã được thiết lập thành công!');
        } else {
            return redirect()->back()->with('error', 'Không thể thiết lập webhook.');
        }
    }

    public function testQuestion(Request $request)
    {
        $request->validate([
            'telegram_id' => 'required|integer',
            'vocab_id' => 'required|exists:vocabularies,id',
        ]);

        $vocabulary = Vocabulary::find($request->vocab_id);
        $choices = $vocabulary->shuffled_choices;

        $success = $this->telegramService->sendVocabularyQuestion(
            $request->telegram_id,
            $vocabulary->word,
            $choices,
            $vocabulary->id
        );

        if ($success) {
            return redirect()->back()->with('success', 'Câu hỏi test đã được gửi!');
        } else {
            return redirect()->back()->with('error', 'Không thể gửi câu hỏi test.');
        }
    }

    public function importVocabulary(Request $request)
    {
        if ($request->isMethod('post')) {
            $request->validate([
                'vocabulary_file' => 'required|file|mimes:txt,csv|max:2048',
                'batch_size' => 'integer|min:1|max:50',
            ]);

            $file = $request->file('vocabulary_file');
            $batchSize = $request->input('batch_size', 10);

            // Save uploaded file temporarily
            $fileName = 'import_' . time() . '.' . $file->getClientOriginalExtension();
            $filePath = $file->storeAs('imports', $fileName, 'local');
            $fullPath = storage_path('app/' . $filePath);

            try {
                // Process the import
                $result = $this->processVocabularyImport($fullPath, $batchSize);

                // Clean up temporary file
                unlink($fullPath);

                $message = "✅ Import hoàn thành! ";
                $message .= "Đã import: {$result['imported']}, ";
                $message .= "Bỏ qua: {$result['skipped']}, ";
                $message .= "Lỗi: {$result['failed']}";

                return redirect()->back()->with('success', $message);

            } catch (\Exception $e) {
                // Clean up on error
                if (file_exists($fullPath)) {
                    unlink($fullPath);
                }

                return redirect()->back()->with('error', 'Lỗi import: ' . $e->getMessage());
            }
        }

        $totalVocabs = Vocabulary::count();
        return view('admin.import-vocabulary', compact('totalVocabs'));
    }

    private function processVocabularyImport(string $filePath, int $batchSize): array
    {
        $words = $this->readWordsFromFile($filePath);

        $stats = [
            'total' => count($words),
            'imported' => 0,
            'skipped' => 0,
            'failed' => 0
        ];

        $batches = array_chunk($words, $batchSize);

        foreach ($batches as $batch) {
            foreach ($batch as $word) {
                $result = $this->processWord($word);
                $stats[$result]++;
            }

            // Small delay between batches
            usleep(500000); // 0.5 seconds
        }

        return $stats;
    }

    private function readWordsFromFile(string $filePath): array
    {
        $content = file_get_contents($filePath);
        $lines = explode("\n", $content);

        $words = [];
        foreach ($lines as $line) {
            $word = trim($line);

            // Skip empty lines and Vietnamese section headers
            if (empty($word) || $word === 'Từ vựng') {
                continue;
            }

            // Clean up the word
            $word = preg_replace('/[^\w\s\-\'\.]/u', '', $word);
            $word = trim($word);

            if (!empty($word) && strlen($word) > 1) {
                $words[] = $word;
            }
        }

        return array_unique($words);
    }

    private function processWord(string $word): string
    {
        try {
            // Check if word already exists
            if (Vocabulary::where('word', $word)->exists()) {
                return 'skipped';
            }

            // Generate vocabulary data using OpenAI
            $aiData = $this->vocabularyService->createVocabulary($word);

            if (!$aiData) {
                return 'failed';
            }

            return 'imported';

        } catch (\Exception $e) {
            \Log::error("Error processing word '{$word}': " . $e->getMessage());
            return 'failed';
        }
    }
