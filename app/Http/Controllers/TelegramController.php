<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\TelegramService;
use App\Services\VocabularyService;
use App\Models\TelegramUser;
use App\Models\UserVocabLog;
use Illuminate\Support\Facades\Log;

class TelegramController extends Controller
{
    private TelegramService $telegramService;
    private VocabularyService $vocabularyService;

    public function __construct(TelegramService $telegramService, VocabularyService $vocabularyService)
    {
        $this->telegramService = $telegramService;
        $this->vocabularyService = $vocabularyService;
    }

    public function webhook(Request $request)
    {
        $update = $request->all();
        Log::info('Telegram webhook received', $update);

        try {
            if (isset($update['message'])) {
                $this->handleMessage($update['message']);
            } elseif (isset($update['callback_query'])) {
                $this->handleCallbackQuery($update['callback_query']);
            }
        } catch (\Exception $e) {
            Log::error('Telegram webhook error: ' . $e->getMessage());
        }

        return response()->json(['ok' => true]);
    }

    private function handleMessage(array $message)
    {
        $chatId = $message['chat']['id'];
        $text = $message['text'] ?? '';
        $user = $message['from'];

        // Create or update user
        $telegramUser = $this->createOrUpdateUser($user);

        switch ($text) {
            case '/start':
                $this->handleStartCommand($chatId, $telegramUser);
                break;
            case '/stats':
                $this->handleStatsCommand($chatId, $telegramUser);
                break;
            case '/schedule':
                $this->handleScheduleCommand($chatId);
                break;
            case '/stop':
                $this->handleStopCommand($chatId, $telegramUser);
                break;
            default:
                $this->handleUnknownCommand($chatId);
        }
    }

    private function handleCallbackQuery(array $callbackQuery)
    {
        $chatId = $callbackQuery['message']['chat']['id'];
        $messageId = $callbackQuery['message']['message_id'];
        $data = $callbackQuery['data'];
        $callbackQueryId = $callbackQuery['id'];
        $user = $callbackQuery['from'];

        $telegramUser = $this->createOrUpdateUser($user);

        if (strpos($data, 'answer_') === 0) {
            $this->handleAnswerCallback($chatId, $messageId, $data, $callbackQueryId, $telegramUser);
        } elseif (strpos($data, 'schedule_') === 0) {
            $this->handleScheduleCallback($chatId, $messageId, $data, $callbackQueryId, $telegramUser);
        }
    }

    private function createOrUpdateUser(array $userData): TelegramUser
    {
        return TelegramUser::updateOrCreate(
            ['telegram_id' => $userData['id']],
            [
                'first_name' => $userData['first_name'],
                'last_name' => $userData['last_name'] ?? null,
                'username' => $userData['username'] ?? null,
                'language_code' => $userData['language_code'] ?? 'en',
                'last_activity' => now(),
            ]
        );
    }

    private function handleStartCommand(int $chatId, TelegramUser $user)
    {
        $text = "🎉 Chào mừng bạn đến với hệ thống học từ vựng tiếng Anh!\n\n";
        $text .= "📚 Tôi sẽ giúp bạn học từ vựng thông qua câu hỏi trắc nghiệm.\n";
        $text .= "🧠 Sử dụng thuật toán lặp lại ngắt quãng để tối ưu hóa việc ghi nhớ.\n\n";
        $text .= "Các lệnh có sẵn:\n";
        $text .= "/stats - Xem tiến độ học tập\n";
        $text .= "/schedule - Thiết lập thời gian học\n";
        $text .= "/stop - Tạm dừng học\n\n";
        $text .= "Bắt đầu thôi! 🚀";

        $this->telegramService->sendMessage($chatId, $text);

        // Add some initial vocabularies for the user
        $added = $this->vocabularyService->addVocabulariesForUser($user, 10);
        if ($added > 0) {
            $this->telegramService->sendMessage($chatId, "✅ Đã thêm {$added} từ vựng mới cho bạn!");
        }
    }

    private function handleStatsCommand(int $chatId, TelegramUser $user)
    {
        $stats = $this->vocabularyService->getUserStats($user);

        $text = "📊 <b>Thống kê học tập của bạn:</b>\n\n";
        $text .= "📚 Tổng số từ: {$stats['total_words']}\n";
        $text .= "🏆 Đã thành thạo: {$stats['mastered_words']}\n";
        $text .= "📖 Đang học: {$stats['learning_words']}\n";
        $text .= "🆕 Từ mới: {$stats['new_words']}\n\n";
        $text .= "✅ Trả lời đúng: {$stats['total_correct']}\n";
        $text .= "❌ Trả lời sai: {$stats['total_incorrect']}\n";
        $text .= "🎯 Độ chính xác: {$stats['accuracy']}%";

        $this->telegramService->sendMessage($chatId, $text);
    }

    private function handleScheduleCommand(int $chatId)
    {
        $text = "⏰ Chọn thời gian bạn muốn nhận câu hỏi từ vựng:";

        $keyboard = [
            'inline_keyboard' => [
                [
                    ['text' => '🌅 Buổi sáng (8:00)', 'callback_data' => 'schedule_morning'],
                    ['text' => '☀️ Buổi trưa (12:00)', 'callback_data' => 'schedule_afternoon']
                ],
                [
                    ['text' => '🌙 Buổi tối (20:00)', 'callback_data' => 'schedule_evening'],
                    ['text' => '🔄 Tất cả', 'callback_data' => 'schedule_all']
                ]
            ]
        ];

        $this->telegramService->sendMessage($chatId, $text, $keyboard);
    }

    private function handleStopCommand(int $chatId, TelegramUser $user)
    {
        $user->update(['is_active' => false]);
        $text = "⏸️ Đã tạm dừng việc gửi câu hỏi từ vựng.\n";
        $text .= "Gửi /start để tiếp tục học.";
        $this->telegramService->sendMessage($chatId, $text);
    }

    private function handleUnknownCommand(int $chatId)
    {
        $text = "❓ Lệnh không được nhận diện.\n\n";
        $text .= "Các lệnh có sẵn:\n";
        $text .= "/start - Bắt đầu học\n";
        $text .= "/stats - Xem thống kê\n";
        $text .= "/schedule - Thiết lập thời gian\n";
        $text .= "/stop - Tạm dừng";

        $this->telegramService->sendMessage($chatId, $text);
    }

    private function handleAnswerCallback(int $chatId, int $messageId, string $data, string $callbackQueryId, TelegramUser $user)
    {
        // Parse callback data: answer_{vocab_id}_{selected_index}
        $parts = explode('_', $data);
        $vocabId = (int)$parts[1];
        $selectedIndex = (int)$parts[2];

        $log = UserVocabLog::where('telegram_user_id', $user->id)
            ->where('vocabulary_id', $vocabId)
            ->with('vocabulary')
            ->first();

        if (!$log) {
            $this->telegramService->answerCallbackQuery($callbackQueryId, 'Không tìm thấy câu hỏi này!');
            return;
        }

        $vocabulary = $log->vocabulary;
        $isCorrect = $this->vocabularyService->processAnswer($log, $selectedIndex);

        if ($isCorrect) {
            $text = "✅ <b>Chính xác!</b>\n\n";
            $text .= "📚 <b>{$vocabulary->word}</b> = {$vocabulary->definition}\n\n";
            $text .= "🔄 Từ này sẽ được lặp lại sau " . $this->getNextShowText($log->success_count) . ".";

            $this->telegramService->answerCallbackQuery($callbackQueryId, '✅ Đúng rồi!');
        } else {
            $text = "❌ <b>Sai rồi!</b>\n\n";
            $text .= "📚 <b>{$vocabulary->word}</b> = {$vocabulary->definition}\n\n";
            if ($vocabulary->tip) {
                $text .= "💡 <b>Mẹo ghi nhớ:</b> {$vocabulary->tip}\n\n";
            }
            $text .= "🔄 Từ này sẽ được hỏi lại vào ngày mai.";

            $this->telegramService->answerCallbackQuery($callbackQueryId, '❌ Sai rồi!');
        }

        $this->telegramService->editMessage($chatId, $messageId, $text);
    }

    private function handleScheduleCallback(int $chatId, int $messageId, string $data, string $callbackQueryId, TelegramUser $user)
    {
        $schedule = str_replace('schedule_', '', $data);

        $schedules = [
            'morning' => ['morning'],
            'afternoon' => ['afternoon'],
            'evening' => ['evening'],
            'all' => ['morning', 'afternoon', 'evening']
        ];

        $user->update(['schedule_times' => $schedules[$schedule] ?? ['morning']]);

        $scheduleText = [
            'morning' => '🌅 Buổi sáng (8:00)',
            'afternoon' => '☀️ Buổi trưa (12:00)',
            'evening' => '🌙 Buổi tối (20:00)',
            'all' => '🔄 Tất cả các buổi'
        ];

        $text = "✅ Đã thiết lập thời gian học: " . $scheduleText[$schedule];

        $this->telegramService->editMessage($chatId, $messageId, $text);
        $this->telegramService->answerCallbackQuery($callbackQueryId, 'Đã cập nhật!');
    }

    private function getNextShowText(int $successCount): string
    {
        $intervals = [
            0 => '1 ngày',
            1 => '3 ngày',
            2 => '1 tuần',
            3 => '2 tuần',
            4 => '1 tháng'
        ];

        return $intervals[$successCount] ?? '1 tháng';
    }
}
