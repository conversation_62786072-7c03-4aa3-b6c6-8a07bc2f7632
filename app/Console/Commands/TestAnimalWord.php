<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Vocabulary;

class TestAnimalWord extends Command
{
    protected $signature = 'test:animal-word';
    protected $description = 'Test the animal word specifically';

    public function handle()
    {
        $this->info('🐾 Testing Animal Word...');
        
        $vocabulary = Vocabulary::where('word', 'animal')->first();
        
        if (!$vocabulary) {
            $this->error('❌ Animal word not found in database');
            return;
        }
        
        $this->info("📚 Word: {$vocabulary->word}");
        $this->info("✅ Correct answer: {$vocabulary->choices[0]}");
        $this->info("📝 All choices: " . json_encode($vocabulary->choices));
        
        // Test the new logic
        $choicesData = $vocabulary->getShuffledChoicesWithCorrectIndex();
        $choices = $choicesData['choices'];
        $correctIndex = $choicesData['correct_index'];
        
        $this->info("\n🔀 After shuffle:");
        foreach ($choices as $index => $choice) {
            $marker = $index === $correctIndex ? ' ✅ CORRECT' : '';
            $this->info("  {$index}. {$choice}{$marker}");
        }
        
        $this->info("\n🎯 Correct index: {$correctIndex}");
        $this->info("🎯 Correct choice: {$choices[$correctIndex]}");
        
        // Verify logic
        if ($choices[$correctIndex] === $vocabulary->choices[0]) {
            $this->info("✅ Logic is working correctly!");
        } else {
            $this->error("❌ Logic error detected!");
        }
        
        // Test cache simulation
        $cacheKey = "correct_answer_{$vocabulary->id}_123456";
        cache()->put($cacheKey, $correctIndex, 300);
        
        $retrievedIndex = cache()->get($cacheKey);
        $this->info("\n💾 Cache test:");
        $this->info("Stored index: {$correctIndex}");
        $this->info("Retrieved index: {$retrievedIndex}");
        
        if ($retrievedIndex === $correctIndex) {
            $this->info("✅ Cache is working correctly!");
        } else {
            $this->error("❌ Cache error!");
        }
        
        cache()->forget($cacheKey);
        
        return 0;
    }
}
