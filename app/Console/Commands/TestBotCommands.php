<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\TelegramService;

class TestBotCommands extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'bot:test-commands {chat_id}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test bot commands by sending help message';

    private TelegramService $telegramService;

    public function __construct(TelegramService $telegramService)
    {
        parent::__construct();
        $this->telegramService = $telegramService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $chatId = (int) $this->argument('chat_id');

        $this->info("🤖 Sending test commands to chat ID: {$chatId}");

        $text = "🎯 <b>Hệ thống học từ vựng tiếng Anh</b>\n\n";
        $text .= "🚀 <b>Session học liên tục:</b>\n";
        $text .= "/learn - Bắt đầu session học\n";
        $text .= "• Sau mỗi câu trả lời → tự động sang từ tiếp theo\n";
        $text .= "• Hiển thị tiến độ: (1), (2), (3)...\n";
        $text .= "• Gửi /stop để dừng session bất cứ lúc nào\n\n";
        $text .= "📊 <b>Các lệnh khác:</b>\n";
        $text .= "/start - Bắt đầu học từ vựng\n";
        $text .= "/stats - Xem tiến độ học tập\n";
        $text .= "/schedule - Thiết lập thời gian học\n\n";
        $text .= "💡 <b>Ưu điểm:</b> Không cần chờ đến giờ, học bao nhiêu tùy ý!";

        $success = $this->telegramService->sendMessage($chatId, $text);

        if ($success) {
            $this->info("✅ Test message sent successfully!");
        } else {
            $this->error("❌ Failed to send test message");
        }

        return 0;
    }
}
