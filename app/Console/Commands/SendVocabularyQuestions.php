<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\TelegramUser;
use App\Services\TelegramService;
use App\Services\VocabularyService;
use Carbon\Carbon;

class SendVocabularyQuestions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vocabulary:send-questions {--time=morning}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send vocabulary questions to active users';

    private TelegramService $telegramService;
    private VocabularyService $vocabularyService;

    public function __construct(TelegramService $telegramService, VocabularyService $vocabularyService)
    {
        parent::__construct();
        $this->telegramService = $telegramService;
        $this->vocabularyService = $vocabularyService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $time = $this->option('time');
        $this->info("Sending vocabulary questions for {$time} time...");

        $users = TelegramUser::where('is_active', true)
            ->whereJsonContains('schedule_times', $time)
            ->get();

        $totalSent = 0;

        foreach ($users as $user) {
            $sent = $this->sendQuestionsToUser($user);
            $totalSent += $sent;

            if ($sent > 0) {
                $this->info("Sent {$sent} questions to user {$user->telegram_id}");
            }
        }

        $this->info("Total questions sent: {$totalSent}");
        return 0;
    }

    private function sendQuestionsToUser(TelegramUser $user): int
    {
        // Check if user is already in a learning session
        if ($user->is_in_learning_session) {
            // Send reminder to continue learning session
            $sessionCount = $user->current_session_count;
            $text = "⏰ <b>Nhắc nhở học tập!</b>\n\n";
            $text .= "📚 Bạn đang có session học dở dang ({$sessionCount} từ đã học)\n";
            $text .= "💪 Hãy tiếp tục học để hoàn thành session!\n\n";
            $text .= "🚀 Gửi /learn để tiếp tục\n";
            $text .= "⏹️ Gửi /stop để dừng session hiện tại";

            $success = $this->telegramService->sendMessage($user->telegram_id, $text);
            return $success ? 1 : 0;
        }

        // Send regular scheduled questions
        $dueVocabularies = $this->vocabularyService->getDueVocabularies($user, 3);
        $sent = 0;

        if ($dueVocabularies->isNotEmpty()) {
            // Send reminder to start learning
            $dueCount = $dueVocabularies->count();
            $text = "⏰ <b>Giờ học từ vựng đã đến!</b>\n\n";
            $text .= "📚 Bạn có {$dueCount} từ vựng cần ôn tập\n";
            $text .= "🚀 Gửi /learn để bắt đầu session học\n\n";
            $text .= "💡 Mẹo: Sau mỗi câu trả lời sẽ tự động chuyển sang từ tiếp theo!";

            $success = $this->telegramService->sendMessage($user->telegram_id, $text);
            $sent = $success ? 1 : 0;
        }

        return $sent;
    }
}
