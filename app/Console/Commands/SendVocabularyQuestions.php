<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\TelegramUser;
use App\Services\TelegramService;
use App\Services\VocabularyService;
use Carbon\Carbon;

class SendVocabularyQuestions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vocabulary:send-questions {--time=morning}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Send vocabulary questions to active users';

    private TelegramService $telegramService;
    private VocabularyService $vocabularyService;

    public function __construct(TelegramService $telegramService, VocabularyService $vocabularyService)
    {
        parent::__construct();
        $this->telegramService = $telegramService;
        $this->vocabularyService = $vocabularyService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $time = $this->option('time');
        $this->info("Sending vocabulary questions for {$time} time...");

        $users = TelegramUser::where('is_active', true)
            ->whereJsonContains('schedule_times', $time)
            ->get();

        $totalSent = 0;

        foreach ($users as $user) {
            $sent = $this->sendQuestionsToUser($user);
            $totalSent += $sent;

            if ($sent > 0) {
                $this->info("Sent {$sent} questions to user {$user->telegram_id}");
            }
        }

        $this->info("Total questions sent: {$totalSent}");
        return 0;
    }

    private function sendQuestionsToUser(TelegramUser $user): int
    {
        $dueVocabularies = $this->vocabularyService->getDueVocabularies($user, 5);
        $sent = 0;

        foreach ($dueVocabularies as $log) {
            $vocabulary = $log->vocabulary;
            $choices = $vocabulary->shuffled_choices;

            $success = $this->telegramService->sendVocabularyQuestion(
                $user->telegram_id,
                $vocabulary->word,
                $choices,
                $vocabulary->id
            );

            if ($success) {
                $sent++;
                // Mark as shown
                $log->update(['last_shown_at' => now()]);
            }

            // Small delay between messages
            usleep(500000); // 0.5 seconds
        }

        return $sent;
    }
}
