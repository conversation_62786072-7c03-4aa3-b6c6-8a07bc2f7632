<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\OpenAIService;

class TestNewTips extends Command
{
    protected $signature = 'test:new-tips {word}';
    protected $description = 'Test new tip generation method for a specific word';

    private OpenAIService $openAIService;

    public function __construct(OpenAIService $openAIService)
    {
        parent::__construct();
        $this->openAIService = $openAIService;
    }

    public function handle()
    {
        $word = $this->argument('word');
        
        $this->info("🧪 Testing new tip generation for: {$word}");
        
        // Test full vocabulary data generation
        $this->info("\n📚 Testing full vocabulary data generation...");
        $aiData = $this->openAIService->generateVocabularyData($word);
        
        if ($aiData) {
            $this->info("✅ Definition: " . $aiData['definition']);
            $this->info("✅ Wrong choices: " . json_encode($aiData['wrong_choices']));
            $this->info("✅ Tip: " . $aiData['tip']);
        } else {
            $this->error("❌ Failed to generate vocabulary data");
        }
        
        // Test tip-only generation
        if ($aiData) {
            $this->info("\n💡 Testing tip-only generation...");
            $tipOnly = $this->openAIService->generateTipOnly($word, $aiData['definition']);
            
            if ($tipOnly) {
                $this->info("✅ Tip only: " . $tipOnly);
            } else {
                $this->error("❌ Failed to generate tip only");
            }
        }
        
        return 0;
    }
}
