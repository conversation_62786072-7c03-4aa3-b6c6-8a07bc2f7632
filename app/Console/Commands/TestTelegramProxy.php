<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\TelegramService;
use GuzzleHttp\Client;

class TestTelegramProxy extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'telegram:test-proxy {--chat-id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Telegram proxy connection';

    private TelegramService $telegramService;

    public function __construct(TelegramService $telegramService)
    {
        parent::__construct();
        $this->telegramService = $telegramService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🔧 Testing Telegram Proxy Connection...');

        // Test 1: Check bot info via proxy
        $this->testBotInfo();

        // Test 2: Send test message if chat ID provided
        $chatId = $this->option('chat-id');
        if ($chatId) {
            $this->testSendMessage((int)$chatId);
        } else {
            $this->warn('💡 Tip: Use --chat-id=YOUR_CHAT_ID to test sending messages');
        }

        return 0;
    }

    private function testBotInfo(): void
    {
        try {
            $client = new Client();
            $botToken = config('telegram.bot_token');
            $apiUrl = config('telegram.api_url') . $botToken;

            $this->info("📡 Testing connection to: " . config('telegram.api_url'));
            $this->info("🤖 Bot Token: " . substr($botToken, 0, 10) . "...");

            $response = $client->get($apiUrl . '/getMe');
            $data = json_decode($response->getBody()->getContents(), true);

            if ($data['ok']) {
                $bot = $data['result'];
                $this->info("✅ Proxy connection successful!");
                $this->info("🤖 Bot Name: " . $bot['first_name']);
                $this->info("👤 Username: @" . $bot['username']);
                $this->info("🆔 Bot ID: " . $bot['id']);
            } else {
                $this->error("❌ Bot API returned error: " . ($data['description'] ?? 'Unknown error'));
            }

        } catch (\Exception $e) {
            $this->error("❌ Proxy connection failed: " . $e->getMessage());
        }
    }

    private function testSendMessage(int $chatId): void
    {
        $this->info("\n📤 Testing message sending to chat ID: {$chatId}");

        $testMessage = "🧪 Test message from Laravel Bot via proxy\n";
        $testMessage .= "⏰ Time: " . now()->format('Y-m-d H:i:s') . "\n";
        $testMessage .= "🌐 Proxy: telegram.devqanh.workers.dev\n";
        $testMessage .= "✅ Connection successful!";

        $success = $this->telegramService->sendMessage($chatId, $testMessage);

        if ($success) {
            $this->info("✅ Test message sent successfully!");
        } else {
            $this->error("❌ Failed to send test message");
        }
    }
}
