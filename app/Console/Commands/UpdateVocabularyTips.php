<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Vocabulary;
use App\Services\OpenAIService;

class UpdateVocabularyTips extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vocabulary:update-tips {--limit=10 : Number of vocabularies to update} {--force : Update all tips, even existing ones} {--word= : Update specific word only}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update vocabulary tips using new "Giải thích gốc từ đơn giản" method';

    private OpenAIService $openAIService;

    public function __construct(OpenAIService $openAIService)
    {
        parent::__construct();
        $this->openAIService = $openAIService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🎯 Cập nhật mẹo ghi nhớ từ vựng với phương pháp "G<PERSON><PERSON>i thích gốc từ đơn giản"');

        $limit = (int) $this->option('limit');
        $force = $this->option('force');
        $specificWord = $this->option('word');

        // Build query
        $query = Vocabulary::query();

        if ($specificWord) {
            $query->where('word', strtolower(trim($specificWord)));
            $this->info("🎯 Cập nhật cho từ cụ thể: {$specificWord}");
        } else {
            if (!$force) {
                // Only update vocabularies without tips or with old tips
                $query->where(function($q) {
                    $q->whereNull('tip')
                      ->orWhere('tip', '')
                      ->orWhere('tip', 'not like', '%→%'); // Old tips don't have arrow symbol
                });
                $this->info("📝 Chỉ cập nhật từ chưa có tip hoặc tip cũ");
            } else {
                $this->info("🔄 Cập nhật tất cả từ vựng (force mode)");
            }

            $query->limit($limit);
        }

        $vocabularies = $query->get();

        if ($vocabularies->isEmpty()) {
            $this->info("✅ Không có từ vựng nào cần cập nhật!");
            return 0;
        }

        $this->info("📚 Tìm thấy {$vocabularies->count()} từ vựng cần cập nhật");

        $progressBar = $this->output->createProgressBar($vocabularies->count());
        $progressBar->start();

        $updated = 0;
        $failed = 0;

        foreach ($vocabularies as $vocabulary) {
            try {
                $this->line("\n🔄 Đang cập nhật: {$vocabulary->word}");

                // Generate new tip using OpenAI (tip only method for better performance)
                $newTip = $this->openAIService->generateTipOnly($vocabulary->word, $vocabulary->definition);

                if ($newTip) {
                    $oldTip = $vocabulary->tip;

                    $vocabulary->update(['tip' => $newTip]);

                    $this->line("✅ Cập nhật thành công!");
                    $this->line("   Cũ: " . ($oldTip ?: '(trống)'));
                    $this->line("   Mới: {$newTip}");

                    $updated++;
                } else {
                    $this->line("❌ Không thể tạo tip mới");
                    $failed++;
                }

                $progressBar->advance();

                // Small delay to avoid rate limiting
                sleep(1);

            } catch (\Exception $e) {
                $this->line("❌ Lỗi: " . $e->getMessage());
                $failed++;
                $progressBar->advance();
            }
        }

        $progressBar->finish();

        $this->newLine(2);
        $this->info("🎉 Hoàn thành cập nhật!");
        $this->info("✅ Thành công: {$updated} từ");
        $this->info("❌ Thất bại: {$failed} từ");

        if ($updated > 0) {
            $this->info("💡 Các từ đã được cập nhật với mẹo ghi nhớ mới sử dụng phương pháp 'Giải thích gốc từ đơn giản'");
        }

        return 0;
    }
}
