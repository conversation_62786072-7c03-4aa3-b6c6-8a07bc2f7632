<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Vocabulary;
use App\Services\OpenAIService;
use Illuminate\Support\Facades\File;

class ImportVocabulary extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'vocabulary:import {file=vocabulary.txt} {--batch=10} {--delay=2}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import vocabulary from text file with AI-generated questions';

    private OpenAIService $openAIService;

    public function __construct(OpenAIService $openAIService)
    {
        parent::__construct();
        $this->openAIService = $openAIService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $filePath = $this->argument('file');
        $batchSize = (int) $this->option('batch');
        $delay = (int) $this->option('delay');

        if (!File::exists($filePath)) {
            $this->error("File not found: {$filePath}");
            return 1;
        }

        $this->info("🚀 Starting vocabulary import from: {$filePath}");
        $this->info("📦 Batch size: {$batchSize} words");
        $this->info("⏱️ Delay between batches: {$delay} seconds");

        $words = $this->readWordsFromFile($filePath);
        $this->info("📚 Found " . count($words) . " words in file");

        $stats = [
            'total' => count($words),
            'imported' => 0,
            'skipped' => 0,
            'failed' => 0
        ];

        $batches = array_chunk($words, $batchSize);
        $progressBar = $this->output->createProgressBar(count($words));
        $progressBar->start();

        foreach ($batches as $batchIndex => $batch) {
            $this->info("\n🔄 Processing batch " . ($batchIndex + 1) . "/" . count($batches));

            foreach ($batch as $word) {
                $result = $this->processWord($word);
                $stats[$result]++;
                $progressBar->advance();
            }

            // Delay between batches to avoid API rate limits
            if ($batchIndex < count($batches) - 1) {
                $this->info("\n⏳ Waiting {$delay} seconds before next batch...");
                sleep($delay);
            }
        }

        $progressBar->finish();
        $this->displayResults($stats);

        return 0;
    }

    private function readWordsFromFile(string $filePath): array
    {
        $content = File::get($filePath);
        $lines = explode("\n", $content);

        $words = [];
        foreach ($lines as $line) {
            $word = trim($line);

            // Skip empty lines and Vietnamese section headers
            if (empty($word) || $word === 'Từ vựng') {
                continue;
            }

            // Clean up the word (remove extra spaces, special characters if needed)
            $word = preg_replace('/[^\w\s\-\'\.]/u', '', $word);
            $word = trim($word);

            if (!empty($word) && strlen($word) > 1) {
                $words[] = $word;
            }
        }

        // Remove duplicates while preserving order
        return array_unique($words);
    }

    private function processWord(string $word): string
    {
        try {
            // Check if word already exists
            $existing = Vocabulary::where('word', $word)->first();
            if ($existing) {
                $this->line(" ⚠️  Skipped: '{$word}' (already exists)");
                return 'skipped';
            }

            // Generate vocabulary data using OpenAI
            $this->line(" 🤖 Generating data for: '{$word}'");
            $aiData = $this->openAIService->generateVocabularyData($word);

            if (!$aiData) {
                $this->line(" ❌ Failed to generate data for: '{$word}'");
                return 'failed';
            }

            // Create vocabulary entry
            $choices = array_merge([$aiData['definition']], $aiData['wrong_choices']);

            Vocabulary::create([
                'word' => $word,
                'definition' => $aiData['definition'],
                'choices' => $choices,
                'tip' => $aiData['tip'],
                'difficulty_level' => $this->determineDifficultyLevel($word),
            ]);

            $this->line(" ✅ Imported: '{$word}'");
            return 'imported';

        } catch (\Exception $e) {
            $this->line(" ❌ Error processing '{$word}': " . $e->getMessage());
            return 'failed';
        }
    }

    private function determineDifficultyLevel(string $word): string
    {
        $length = strlen($word);
        $commonWords = [
            'hello', 'hi', 'good', 'morning', 'afternoon', 'evening', 'nice', 'meet', 'you',
            'how', 'are', 'fine', 'thank', 'what', 'name', 'where', 'from', 'take', 'care',
            'see', 'later', 'father', 'mother', 'son', 'daughter', 'brother', 'sister',
            'friend', 'work', 'job', 'school', 'student', 'teacher', 'house', 'room', 'food',
            'water', 'time', 'day', 'week', 'month', 'year', 'sun', 'rain', 'hot', 'cold'
        ];

        if (in_array(strtolower($word), $commonWords) || $length <= 4) {
            return 'beginner';
        } elseif ($length <= 8) {
            return 'intermediate';
        } else {
            return 'advanced';
        }
    }

    private function displayResults(array $stats): void
    {
        $this->newLine(2);
        $this->info('📊 Import Results:');
        $this->table(
            ['Status', 'Count', 'Percentage'],
            [
                ['✅ Imported', $stats['imported'], round(($stats['imported'] / $stats['total']) * 100, 1) . '%'],
                ['⚠️  Skipped (duplicates)', $stats['skipped'], round(($stats['skipped'] / $stats['total']) * 100, 1) . '%'],
                ['❌ Failed', $stats['failed'], round(($stats['failed'] / $stats['total']) * 100, 1) . '%'],
                ['📚 Total', $stats['total'], '100%'],
            ]
        );

        if ($stats['imported'] > 0) {
            $this->info("🎉 Successfully imported {$stats['imported']} new vocabulary words!");
        }

        if ($stats['failed'] > 0) {
            $this->warn("⚠️  {$stats['failed']} words failed to import. Check logs for details.");
        }

        if ($stats['skipped'] > 0) {
            $this->info("ℹ️  {$stats['skipped']} words were skipped (already exist in database).");
        }
    }
}
