<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Vocabulary;

class CheckVocabTip extends Command
{
    protected $signature = 'check:vocab-tip {word}';
    protected $description = 'Check vocabulary tip for a specific word';

    public function handle()
    {
        $word = $this->argument('word');

        $vocabulary = Vocabulary::where('word', $word)->first();

        if (!$vocabulary) {
            $this->error("❌ Từ '{$word}' không tìm thấy trong database");
            return 1;
        }

        $this->info("📚 Từ vựng: {$vocabulary->word}");
        $this->info("📖 Định nghĩa: {$vocabulary->definition}");
        $this->info("💡 Tip ghi nhớ: " . ($vocabulary->tip ?: '(chưa có)'));

        if ($vocabulary->tip && (strpos($vocabulary->tip, '→') !== false || strpos($vocabulary->tip, 'gốc') !== false || strpos($vocabulary->tip, 'Latin') !== false || strpos($vocabulary->tip, 'Greek') !== false)) {
            $this->info("✅ Tip đã được cập nhật với phương pháp mới!");
        } else {
            $this->warn("⚠️ Tip chưa được cập nhật với phương pháp mới");
        }

        return 0;
    }
}
