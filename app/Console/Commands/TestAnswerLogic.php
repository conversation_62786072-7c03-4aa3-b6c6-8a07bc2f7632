<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Vocabulary;

class TestAnswerLogic extends Command
{
    protected $signature = 'test:answer-logic';
    protected $description = 'Test the new answer logic with shuffled choices';

    public function handle()
    {
        $this->info('🧪 Testing Answer Logic...');
        
        // Get a vocabulary
        $vocabulary = Vocabulary::first();
        
        if (!$vocabulary) {
            $this->error('❌ No vocabulary found in database');
            return;
        }
        
        $this->info("📚 Testing with word: {$vocabulary->word}");
        $this->info("✅ Correct answer: {$vocabulary->choices[0]}");
        
        // Test multiple shuffles
        for ($i = 1; $i <= 5; $i++) {
            $this->info("\n--- Test #{$i} ---");
            
            $choicesData = $vocabulary->getShuffledChoicesWithCorrectIndex();
            $choices = $choicesData['choices'];
            $correctIndex = $choicesData['correct_index'];
            
            $this->info("Shuffled choices:");
            foreach ($choices as $index => $choice) {
                $marker = $index === $correctIndex ? ' ✅' : '';
                $this->info("  {$index}. {$choice}{$marker}");
            }
            
            $this->info("Correct index: {$correctIndex}");
            $this->info("Correct choice: {$choices[$correctIndex]}");
            
            // Verify
            if ($choices[$correctIndex] === $vocabulary->choices[0]) {
                $this->info("✅ Logic is correct!");
            } else {
                $this->error("❌ Logic error!");
            }
        }
        
        $this->info("\n🎉 Test completed!");
        return 0;
    }
}
