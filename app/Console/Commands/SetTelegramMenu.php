<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use GuzzleHttp\Client;

class SetTelegramMenu extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'telegram:set-menu';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Set Telegram bot menu commands';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🤖 Setting Telegram Bot Menu Commands...');

        $botToken = config('telegram.bot_token');
        $apiUrl = config('telegram.api_url') . $botToken;

        $commands = [
            [
                'command' => 'start',
                'description' => '🚀 Bắt đầu học từ vựng'
            ],
            [
                'command' => 'learn',
                'description' => '📚 Bắt đầu session học liên tục'
            ],
            [
                'command' => 'stats',
                'description' => '📊 Xem tiến độ học tập'
            ],
            [
                'command' => 'schedule',
                'description' => '⏰ Thiết lập thời gian học'
            ],
            [
                'command' => 'stop',
                'description' => '⏹️ Dừng session hoặc tạm dừng'
            ]
        ];

        try {
            $client = new Client();
            $response = $client->post($apiUrl . '/setMyCommands', [
                'json' => [
                    'commands' => $commands
                ]
            ]);

            $data = json_decode($response->getBody()->getContents(), true);

            if ($data['ok']) {
                $this->info('✅ Bot menu commands set successfully!');
                $this->info('📱 Users can now see commands in Telegram menu');

                $this->table(['Command', 'Description'], array_map(function($cmd) {
                    return [$cmd['command'], $cmd['description']];
                }, $commands));

            } else {
                $this->error('❌ Failed to set menu: ' . ($data['description'] ?? 'Unknown error'));
            }

        } catch (\Exception $e) {
            $this->error('❌ Error: ' . $e->getMessage());
        }

        return 0;
    }
}
