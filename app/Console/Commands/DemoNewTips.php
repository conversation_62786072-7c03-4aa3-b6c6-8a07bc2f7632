<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Vocabulary;

class DemoNewTips extends Command
{
    protected $signature = 'demo:new-tips';
    protected $description = 'Demo new tip generation method with sample data';

    public function handle()
    {
        $this->info('🎯 Demo phương pháp "Giải thích gốc từ đơn giản"');
        $this->info('');

        // Sample tips using the new method
        $sampleTips = [
            'telephone' => [
                'definition' => 'điện thoại',
                'old_tip' => 'Thiết bị liên lạc từ xa',
                'new_tip' => '"tele" (xa) + "phone" (âm thanh) → thiết bị truyền âm thanh đi xa'
            ],
            'biology' => [
                'definition' => 'sinh học',
                'old_tip' => 'Môn khoa học về sự sống',
                'new_tip' => '"bio" (sống) + "logy" (học) → môn học về sự sống'
            ],
            'transport' => [
                'definition' => 'vận chuyển',
                'old_tip' => '<PERSON> chuyển hàng hóa từ nơi này sang nơi khác',
                'new_tip' => '"trans" (qua) + "port" (mang) → mang đồ vật qua chỗ khác'
            ],
            'photograph' => [
                'definition' => 'bức ảnh',
                'old_tip' => 'Hình ảnh được chụp bằng máy ảnh',
                'new_tip' => '"photo" (ánh sáng) + "graph" (viết/vẽ) → vẽ bằng ánh sáng'
            ],
            'international' => [
                'definition' => 'quốc tế',
                'old_tip' => 'Liên quan đến nhiều quốc gia',
                'new_tip' => '"inter" (giữa) + "nation" (quốc gia) → giữa các quốc gia'
            ],
            'television' => [
                'definition' => 'tivi, truyền hình',
                'old_tip' => 'Thiết bị xem chương trình',
                'new_tip' => '"tele" (xa) + "vision" (nhìn) → nhìn thấy hình ảnh từ xa'
            ],
            'automobile' => [
                'definition' => 'ô tô',
                'old_tip' => 'Phương tiện giao thông 4 bánh',
                'new_tip' => '"auto" (tự động) + "mobile" (di chuyển) → tự di chuyển'
            ],
            'geography' => [
                'definition' => 'địa lý',
                'old_tip' => 'Môn học về Trái Đất',
                'new_tip' => '"geo" (đất) + "graphy" (viết) → viết về đất đai'
            ]
        ];

        $this->table(
            ['Từ', 'Nghĩa', 'Tip cũ', 'Tip mới (Giải thích gốc từ)'],
            collect($sampleTips)->map(function($data, $word) {
                return [
                    $word,
                    $data['definition'],
                    $data['old_tip'],
                    $data['new_tip']
                ];
            })->toArray()
        );

        $this->info('');
        $this->info('🎯 Ưu điểm của phương pháp mới:');
        $this->info('✅ Hiểu được nguồn gốc và logic của từ');
        $this->info('✅ Dễ nhớ nhờ phân tích thành phần');
        $this->info('✅ Tăng vốn từ vựng liên quan');
        $this->info('✅ Phương pháp khoa học, có căn cứ');

        $this->info('');
        $this->info('🚀 Để cập nhật tip cho từ vựng hiện có:');
        $this->info('   php artisan vocabulary:update-tips --limit=5');
        $this->info('   php artisan vocabulary:update-tips --word=cat');
        $this->info('   php artisan vocabulary:update-tips --force --limit=10');

        // Update some existing vocabularies with demo tips
        $this->info('');
        if ($this->confirm('Bạn có muốn cập nhật một số từ vựng mẫu với tip mới không?')) {
            $updated = 0;

            foreach ($sampleTips as $word => $data) {
                $vocabulary = Vocabulary::where('word', $word)->first();

                if ($vocabulary) {
                    $oldTip = $vocabulary->tip;
                    $vocabulary->update(['tip' => $data['new_tip']]);

                    $this->info("✅ Cập nhật '{$word}': {$data['new_tip']}");
                    $updated++;
                }
            }

            if ($updated > 0) {
                $this->info("🎉 Đã cập nhật {$updated} từ vựng với tip mới!");
            } else {
                $this->info("ℹ️ Không tìm thấy từ vựng nào trong database để cập nhật.");
            }
        }

        return 0;
    }
}
