<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Vocabulary;

class DemoNewTips extends Command
{
    protected $signature = 'demo:new-tips';
    protected $description = 'Demo new tip generation method with sample data';

    public function handle()
    {
        $this->info('🎯 Demo phương pháp "Gợi âm hài + Tưởng tượng siêu dễ"');
        $this->info('');
        
        // Sample tips using the new method
        $sampleTips = [
            'cat' => [
                'definition' => 'con mèo',
                'old_tip' => 'Động vật có 4 chân, kêu meo meo',
                'new_tip' => 'Cat → Cắt → Cắt lông mèo để mèo đẹp hơn 🐱✂️'
            ],
            'book' => [
                'definition' => 'cuốn sách',
                'old_tip' => 'Vật dụng để đọc và học',
                'new_tip' => 'Book → Búc → Búc đầu vào sách để học giỏi 📚💪'
            ],
            'happy' => [
                'definition' => 'vui vẻ, hạnh phúc',
                'old_tip' => 'Cảm xúc tích cực',
                'new_tip' => 'Happy → Hạp pi → Hạp miệng ăn pizza thì ai cũng vui 🍕😊'
            ],
            'water' => [
                'definition' => 'nước',
                'old_tip' => 'Chất lỏng trong suốt',
                'new_tip' => 'Water → Uốt tơ → Nước làm ướt tơ tằm 💧🐛'
            ],
            'house' => [
                'definition' => 'ngôi nhà',
                'old_tip' => 'Nơi ở của con người',
                'new_tip' => 'House → Hao xơ → Hao xơ tiền xây nhà to 🏠💰'
            ],
            'love' => [
                'definition' => 'yêu thương',
                'old_tip' => 'Tình cảm sâu sắc',
                'new_tip' => 'Love → Lớp → Lớp học yêu thương nhau 💕👫'
            ],
            'money' => [
                'definition' => 'tiền bạc',
                'old_tip' => 'Phương tiện thanh toán',
                'new_tip' => 'Money → Mơ nè → Mơ nè có tiền nhiều vô kể 💰💭'
            ],
            'friend' => [
                'definition' => 'bạn bè',
                'old_tip' => 'Người thân thiết',
                'new_tip' => 'Friend → Phren → Phren (điên) với bạn thân quá vui 🤪👯'
            ]
        ];
        
        $this->table(
            ['Từ', 'Nghĩa', 'Tip cũ', 'Tip mới (Gợi âm hài + Tưởng tượng)'],
            collect($sampleTips)->map(function($data, $word) {
                return [
                    $word,
                    $data['definition'],
                    $data['old_tip'],
                    $data['new_tip']
                ];
            })->toArray()
        );
        
        $this->info('');
        $this->info('🎯 Ưu điểm của phương pháp mới:');
        $this->info('✅ Dễ nhớ hơn nhờ kết nối âm thanh');
        $this->info('✅ Vui nhộn, hài hước tạo ấn tượng sâu');
        $this->info('✅ Tưởng tượng trực quan giúp ghi nhớ lâu');
        $this->info('✅ Phù hợp với người Việt Nam');
        
        $this->info('');
        $this->info('🚀 Để cập nhật tip cho từ vựng hiện có:');
        $this->info('   php artisan vocabulary:update-tips --limit=5');
        $this->info('   php artisan vocabulary:update-tips --word=cat');
        $this->info('   php artisan vocabulary:update-tips --force --limit=10');
        
        // Update some existing vocabularies with demo tips
        $this->info('');
        if ($this->confirm('Bạn có muốn cập nhật một số từ vựng mẫu với tip mới không?')) {
            $updated = 0;
            
            foreach ($sampleTips as $word => $data) {
                $vocabulary = Vocabulary::where('word', $word)->first();
                
                if ($vocabulary) {
                    $oldTip = $vocabulary->tip;
                    $vocabulary->update(['tip' => $data['new_tip']]);
                    
                    $this->info("✅ Cập nhật '{$word}': {$data['new_tip']}");
                    $updated++;
                }
            }
            
            if ($updated > 0) {
                $this->info("🎉 Đã cập nhật {$updated} từ vựng với tip mới!");
            } else {
                $this->info("ℹ️ Không tìm thấy từ vựng nào trong database để cập nhật.");
            }
        }
        
        return 0;
    }
}
