<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class UserVocabLog extends Model
{
    protected $fillable = [
        'telegram_user_id',
        'vocabulary_id',
        'next_show',
        'success_count',
        'fail_count',
        'last_result',
        'last_shown_at',
    ];

    protected $casts = [
        'next_show' => 'datetime',
        'last_shown_at' => 'datetime',
        'success_count' => 'integer',
        'fail_count' => 'integer',
    ];

    public function telegramUser(): BelongsTo
    {
        return $this->belongsTo(TelegramUser::class);
    }

    public function vocabulary(): BelongsTo
    {
        return $this->belongsTo(Vocabulary::class);
    }

    public function isDue(): bool
    {
        return $this->next_show <= now();
    }

    public function markCorrect(): void
    {
        $this->success_count++;
        $this->last_result = 'correct';
        $this->last_shown_at = now();
        $this->next_show = $this->calculateNextShow();
        $this->save();
    }

    public function markIncorrect(): void
    {
        $this->success_count = 0;
        $this->fail_count++;
        $this->last_result = 'incorrect';
        $this->last_shown_at = now();
        $this->next_show = now()->addDay(); // Show again tomorrow
        $this->save();
    }

    private function calculateNextShow(): Carbon
    {
        $intervals = [
            0 => 1,   // 1 day
            1 => 3,   // 3 days
            2 => 7,   // 1 week
            3 => 14,  // 2 weeks
            4 => 30,  // 1 month
        ];

        $days = $intervals[$this->success_count] ?? 30;
        return now()->addDays($days);
    }
}
