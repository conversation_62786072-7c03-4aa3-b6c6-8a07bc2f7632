<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class TelegramUser extends Model
{
    protected $fillable = [
        'telegram_id',
        'first_name',
        'last_name',
        'username',
        'language_code',
        'is_active',
        'schedule_times',
        'last_activity',
        'is_in_learning_session',
        'current_session_count',
        'session_started_at',
    ];

    protected $casts = [
        'telegram_id' => 'integer',
        'is_active' => 'boolean',
        'schedule_times' => 'array',
        'last_activity' => 'datetime',
        'is_in_learning_session' => 'boolean',
        'current_session_count' => 'integer',
        'session_started_at' => 'datetime',
    ];

    public function userVocabLogs(): HasMany
    {
        return $this->hasMany(UserVocabLog::class);
    }

    public function getFullNameAttribute(): string
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    public function isScheduledTime(string $time): bool
    {
        return in_array($time, $this->schedule_times ?? []);
    }

    public function startLearningSession(): void
    {
        $this->update([
            'is_in_learning_session' => true,
            'current_session_count' => 0,
            'session_started_at' => now(),
        ]);
    }

    public function incrementSessionCount(): void
    {
        $this->increment('current_session_count');
    }

    public function endLearningSession(): void
    {
        $this->update([
            'is_in_learning_session' => false,
            'current_session_count' => 0,
            'session_started_at' => null,
        ]);
    }
}
