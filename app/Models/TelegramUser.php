<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class TelegramUser extends Model
{
    protected $fillable = [
        'telegram_id',
        'first_name',
        'last_name',
        'username',
        'language_code',
        'is_active',
        'schedule_times',
        'last_activity',
    ];

    protected $casts = [
        'telegram_id' => 'integer',
        'is_active' => 'boolean',
        'schedule_times' => 'array',
        'last_activity' => 'datetime',
    ];

    public function userVocabLogs(): HasMany
    {
        return $this->hasMany(UserVocabLog::class);
    }

    public function getFullNameAttribute(): string
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    public function isScheduledTime(string $time): bool
    {
        return in_array($time, $this->schedule_times ?? []);
    }
}
