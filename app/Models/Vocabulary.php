<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Vocabulary extends Model
{
    protected $fillable = [
        'word',
        'definition',
        'choices',
        'tip',
        'difficulty_level',
        'usage_count',
    ];

    protected $casts = [
        'choices' => 'array',
        'usage_count' => 'integer',
    ];

    public function userVocabLogs(): HasMany
    {
        return $this->hasMany(UserVocabLog::class);
    }

    public function getShuffledChoicesAttribute(): array
    {
        $choices = $this->choices;

        // Store the correct answer before shuffling
        $correctAnswer = $choices[0];

        // Shuffle the choices
        shuffle($choices);

        // Store the new position of correct answer for later use
        $this->correct_answer_index = array_search($correctAnswer, $choices);

        return $choices;
    }

    public function getShuffledChoicesWithCorrectIndex(): array
    {
        $choices = $this->choices;
        $correctAnswer = $choices[0];

        shuffle($choices);

        $correctIndex = array_search($correctAnswer, $choices);

        return [
            'choices' => $choices,
            'correct_index' => $correctIndex
        ];
    }

    public function getCorrectAnswerAttribute(): string
    {
        return $this->choices[0] ?? '';
    }

    public function incrementUsage(): void
    {
        $this->increment('usage_count');
    }
}
