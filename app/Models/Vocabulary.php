<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Vocabulary extends Model
{
    protected $fillable = [
        'word',
        'definition',
        'choices',
        'tip',
        'difficulty_level',
        'usage_count',
    ];

    protected $casts = [
        'choices' => 'array',
        'usage_count' => 'integer',
    ];

    public function userVocabLogs(): HasMany
    {
        return $this->hasMany(UserVocabLog::class);
    }

    public function getShuffledChoicesAttribute(): array
    {
        $choices = $this->choices;
        shuffle($choices);
        return $choices;
    }

    public function getCorrectAnswerAttribute(): string
    {
        return $this->choices[0] ?? '';
    }

    public function incrementUsage(): void
    {
        $this->increment('usage_count');
    }
}
