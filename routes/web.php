<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AdminController;

Route::get('/', function () {
    return view('welcome');
});

// Admin routes
Route::prefix('admin')->name('admin.')->group(function () {
    Route::get('/', [AdminController::class, 'dashboard'])->name('dashboard');
    Route::match(['GET', 'POST'], '/add-vocabulary', [AdminController::class, 'addVocabulary'])->name('add-vocabulary');
    Route::match(['GET', 'POST'], '/import-vocabulary', [AdminController::class, 'importVocabulary'])->name('import-vocabulary');
    Route::post('/webhook', [AdminController::class, 'setWebhook'])->name('webhook');
    Route::post('/test-question', [AdminController::class, 'testQuestion'])->name('test-question');
});
