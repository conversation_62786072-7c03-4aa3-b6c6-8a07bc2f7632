<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AdminController;

Route::get('/', function () {
    return view('welcome');
});

// Admin routes
Route::prefix('admin')->name('admin.')->group(function () {
    Route::get('/', [AdminController::class, 'dashboard'])->name('dashboard');
    Route::match(['GET', 'POST'], '/add-vocabulary', [AdminController::class, 'addVocabulary'])->name('add-vocabulary');
    Route::match(['GET', 'POST'], '/import-vocabulary', [AdminController::class, 'importVocabulary'])->name('import-vocabulary');
    Route::get('/cronjob-guide', [AdminController::class, 'cronjobGuide'])->name('cronjob-guide');
    Route::get('/system-info', [AdminController::class, 'systemInfo'])->name('system-info');
    Route::get('/detailed-stats', [AdminController::class, 'detailedStats'])->name('detailed-stats');
    Route::get('/user/{userId}', [AdminController::class, 'userDetail'])->name('user-detail');
    Route::post('/webhook', [AdminController::class, 'setWebhook'])->name('webhook');
    Route::post('/test-question', [AdminController::class, 'testQuestion'])->name('test-question');
    Route::post('/test-proxy', [AdminController::class, 'testProxy'])->name('test-proxy');
    Route::post('/set-telegram-menu', [AdminController::class, 'setTelegramMenu'])->name('set-telegram-menu');
});
