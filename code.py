import requests
from bs4 import BeautifulSoup

# URL chứa từ vựng
url = "https://ieltsarena.edu.vn/tu-vung-tieng-anh-giao-tiep/"

# G<PERSON>i yêu cầu GET và lấy nội dung HTML
res = requests.get(url)
res.raise_for_status()

# Phân tích HTML
soup = BeautifulSoup(res.text, "html.parser")

# Tìm tất cả các dòng trong bảng
rows = soup.select("table tbody tr")

words = []
for tr in rows:
    cols = tr.find_all("td")
    if len(cols) >= 2:
        eng_word = cols[1].get_text(strip=True)
        words.append(eng_word)

# <PERSON><PERSON> danh sách từ ra file txt
with open("vocabulary.txt", "w", encoding="utf-8") as f:
    for w in words:
        f.write(w + "\n")

print(f"Đã trích xuất {len(words)} từ tiếng Anh vào file 'vocabulary.txt'.")
