<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_vocab_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('telegram_user_id')->constrained()->onDelete('cascade');
            $table->foreignId('vocabulary_id')->constrained()->onDelete('cascade');
            $table->timestamp('next_show');
            $table->integer('success_count')->default(0);
            $table->integer('fail_count')->default(0);
            $table->enum('last_result', ['correct', 'incorrect', 'pending'])->default('pending');
            $table->timestamp('last_shown_at')->nullable();
            $table->timestamps();

            $table->unique(['telegram_user_id', 'vocabulary_id']);
            $table->index(['next_show', 'telegram_user_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_vocab_logs');
    }
};
