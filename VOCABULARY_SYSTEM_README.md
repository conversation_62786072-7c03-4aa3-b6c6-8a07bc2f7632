# 🎯 <PERSON>ệ thống học từ vựng tiếng Anh qua Telegram Bot

Hệ thống học từ vựng thông minh sử dụng thuật toán **Spaced Repetition (SM-2)** để tối ưu hóa việc ghi nhớ từ vựng tiếng Anh thông qua Telegram Bot.

## 🚀 Tính năng chính

- **🤖 Telegram Bot tương tác**: Gửi câu hỏi trắc nghiệm tự động
- **🧠 Thuật toán Spaced Repetition**: Lặp lại từ vựng theo khoa học
- **🎨 OpenAI Integration**: Tự động tạo câu hỏi và mẹo ghi nhớ
- **⏰ Lên lịch thông minh**: G<PERSON>i câu hỏi theo thời gian người dùng chọn
- **📊 Thống kê chi tiết**: <PERSON> dõi tiến độ học tập
- **🔧 Admin Panel**: <PERSON>u<PERSON>n lý từ vựng và người dùng

## 🛠️ Công nghệ sử dụng

- **Backend**: Laravel 12 (PHP 8.2+)
- **Database**: MySQL
- **AI**: OpenAI GPT-4o-mini
- **Bot**: Telegram Bot API
- **Scheduler**: Laravel Cron Jobs

## 📋 Cài đặt

### 1. Cấu hình database trong `.env`
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=hoc-tieng-anh
DB_USERNAME=root
DB_PASSWORD=
```

### 2. Cấu hình Telegram Bot và OpenAI
```env
# Telegram Bot
TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_WEBHOOK_URL=https://yourdomain.com/api/telegram/webhook
TELEGRAM_API_URL=https://telegram.devqanh.workers.dev/bot

# OpenAI
OPENAI_API_URL=https://gpt.toptukhoa.com/v1/chat/completions
OPENAI_API_KEY=sk-3p2oKn4UBUL4ZO8IbHgrPYD47xnrItY5Co1v9hEYHut1H1DSTtaPJzAGmMjcFHqy5ueermySn4Q6QhZb
OPENAI_MODEL=gpt-4o-mini
```

> **🌐 Lưu ý về Proxy:** Hệ thống sử dụng proxy `telegram.devqanh.workers.dev` để tránh bị chặn IP tại Việt Nam.

### 3. Chạy migration và seeder
```bash
php artisan migrate
php artisan db:seed --class=VocabularySeeder
```

## 🎮 Sử dụng

### Admin Panel
Truy cập: `http://localhost/admin`

**Chức năng:**
- Thiết lập Webhook cho Telegram Bot
- Thêm từ vựng mới (tự động tạo câu hỏi bằng AI)
- Gửi câu hỏi test
- Xem thống kê hệ thống

### Telegram Bot Commands
- `/start` - Bắt đầu học từ vựng
- `/stats` - Xem tiến độ học tập
- `/schedule` - Thiết lập thời gian nhận câu hỏi
- `/stop` - Tạm dừng học

### Cronjob Setup
Thêm vào crontab để tự động gửi câu hỏi:
```bash
* * * * * cd /path/to/project && php artisan schedule:run >> /dev/null 2>&1
```

## 🔧 API Endpoints

### Telegram Webhook
```
POST /api/telegram/webhook
```

### Admin Routes
```
GET  /admin                    - Dashboard
GET  /admin/add-vocabulary     - Form thêm từ vựng
POST /admin/add-vocabulary     - Xử lý thêm từ vựng
POST /admin/webhook           - Thiết lập webhook
POST /admin/test-question     - Gửi câu hỏi test
```

## 🧠 Thuật toán Spaced Repetition

Hệ thống sử dụng thuật toán SM-2 đơn giản hóa:

- **Lần 1**: Lặp lại sau 1 ngày
- **Lần 2**: Lặp lại sau 3 ngày  
- **Lần 3**: Lặp lại sau 1 tuần
- **Lần 4**: Lặp lại sau 2 tuần
- **Lần 5+**: Lặp lại sau 1 tháng

Nếu trả lời sai → Reset về lần 1

## 📊 Cấu trúc Database

### Bảng `vocabularies`
- `word`: Từ vựng tiếng Anh
- `definition`: Định nghĩa tiếng Việt
- `choices`: JSON array chứa 4 lựa chọn (đáp án đúng + 3 sai)
- `tip`: Mẹo ghi nhớ

### Bảng `telegram_users`
- `telegram_id`: ID Telegram của người dùng
- `schedule_times`: JSON array thời gian nhận câu hỏi
- `is_active`: Trạng thái hoạt động

### Bảng `user_vocab_logs`
- `next_show`: Thời gian hiển thị câu hỏi tiếp theo
- `success_count`: Số lần trả lời đúng liên tiếp
- `last_result`: Kết quả lần trả lời gần nhất

## 🤖 OpenAI Integration

Hệ thống tự động gọi OpenAI API để tạo:
- Định nghĩa tiếng Việt cho từ vựng
- 3 đáp án sai hợp lý
- Mẹo ghi nhớ sáng tạo

**Prompt mẫu:**
```
Tôi cần bạn tạo câu hỏi trắc nghiệm học từ vựng tiếng Anh cho từ: abandon

1. Định nghĩa tiếng Việt:
2. 3 đáp án sai nhưng hợp lý:
3. Mẹo ghi nhớ 1 dòng, dễ nhớ:

Output JSON: {...}
```

## 📱 Demo Telegram Bot

1. Tìm bot trên Telegram: `@your_bot_username`
2. Gửi `/start` để bắt đầu
3. Chọn thời gian nhận câu hỏi với `/schedule`
4. Nhận và trả lời câu hỏi trắc nghiệm
5. Xem tiến độ với `/stats`

## 🔍 Troubleshooting

### Lỗi thường gặp:

**1. Webhook không hoạt động**
- Kiểm tra SSL certificate
- Đảm bảo URL public accessible
- Kiểm tra Telegram Bot Token
- Test proxy: `php artisan telegram:test-proxy`

**2. OpenAI API lỗi**
- Kiểm tra API key và URL
- Kiểm tra quota và rate limit

**3. Cronjob không chạy**
- Kiểm tra crontab setup
- Kiểm tra file permissions
- Xem log: `storage/logs/laravel.log`

## 📞 Hỗ trợ

Nếu gặp vấn đề, vui lòng:
1. Kiểm tra log trong `storage/logs/`
2. Chạy `php artisan config:clear` và `php artisan cache:clear`
3. Kiểm tra database connection

## 📄 License

MIT License - Xem file [LICENSE](LICENSE) để biết thêm chi tiết.
